<template>
  <div class="dashboard">
    <h1>仪表盘</h1>
    
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon user-icon">
              <el-icon><User /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.userCount }}</div>
              <div class="stats-label">用户总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon subject-icon">
              <el-icon><Reading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.subjectCount }}</div>
              <div class="stats-label">学科数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon question-icon">
              <el-icon><EditPen /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.questionCount }}</div>
              <div class="stats-label">题目数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon note-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.noteCount }}</div>
              <div class="stats-label">笔记数量</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>用户注册趋势</span>
          </template>
          <div class="chart-container" ref="userChartRef"></div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>学科分布</span>
          </template>
          <div class="chart-container" ref="subjectChartRef"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 最近活动 -->
    <el-row :gutter="20" class="activity-row">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>最近活动</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="activity.time"
              :type="activity.type"
            >
              {{ activity.content }}
            </el-timeline-item>
          </el-timeline>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'

// 统计数据
const stats = reactive({
  userCount: 0,
  subjectCount: 0,
  questionCount: 0,
  noteCount: 0
})

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    content: '新用户张三注册成功',
    time: '2024-01-15 10:30',
    type: 'primary'
  },
  {
    id: 2,
    content: '添加了新学科：物理',
    time: '2024-01-15 09:15',
    type: 'success'
  },
  {
    id: 3,
    content: '题目库更新了100道数学题',
    time: '2024-01-14 16:45',
    type: 'info'
  },
  {
    id: 4,
    content: '系统维护完成',
    time: '2024-01-14 14:20',
    type: 'warning'
  }
])

const userChartRef = ref()
const subjectChartRef = ref()

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里应该调用API获取真实数据
    stats.userCount = 1250
    stats.subjectCount = 8
    stats.questionCount = 5680
    stats.noteCount = 3420
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 初始化图表
const initCharts = () => {
  // 这里可以使用ECharts等图表库
  // 由于简化，暂时不实现具体图表
}

onMounted(() => {
  loadStats()
  initCharts()
})
</script>

<style scoped>
.dashboard {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 20px;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.subject-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.question-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.note-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.charts-row {
  margin-bottom: 20px;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}

.activity-row {
  margin-bottom: 20px;
}
</style>
