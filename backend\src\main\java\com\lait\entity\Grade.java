package com.lait.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 成绩实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "grades")
public class Grade extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "学生ID不能为空")
    @Column(name = "student_id", nullable = false)
    private Long studentId;

    @NotNull(message = "学科ID不能为空")
    @Column(name = "subject_id", nullable = false)
    private Long subjectId;

    @Column(name = "exam_name")
    private String examName; // 考试名称

    @Column(name = "exam_type")
    private String examType; // 考试类型（期中、期末、单元测试等）

    @NotNull(message = "分数不能为空")
    @Column(precision = 5, scale = 2, nullable = false)
    private BigDecimal score;

    @Column(name = "total_score", precision = 5, scale = 2)
    private BigDecimal totalScore; // 总分

    @Column(name = "class_rank")
    private Integer classRank; // 班级排名

    @Column(name = "grade_rank")
    private Integer gradeRank; // 年级排名

    @Column(columnDefinition = "TEXT")
    private String comments; // 评语

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private GradeStatus status;

    /**
     * 成绩状态枚举
     */
    public enum GradeStatus {
        DRAFT("草稿"),
        PUBLISHED("已发布"),
        ARCHIVED("已归档");

        private final String description;

        GradeStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
