package com.lait.repository;

import com.lait.entity.Note;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 笔记数据访问层
 */
@Repository
public interface NoteRepository extends JpaRepository<Note, Long> {

    /**
     * 根据学生ID查找笔记
     */
    List<Note> findByStudentId(Long studentId);

    /**
     * 根据学科ID查找笔记
     */
    List<Note> findBySubjectId(Long subjectId);

    /**
     * 根据学生和学科查找笔记
     */
    List<Note> findByStudentIdAndSubjectId(Long studentId, Long subjectId);

    /**
     * 根据笔记类型查找笔记
     */
    List<Note> findByNoteType(Note.NoteType noteType);

    /**
     * 根据状态查找笔记
     */
    List<Note> findByStatus(Note.NoteStatus status);

    /**
     * 查找共享笔记
     */
    List<Note> findByIsSharedTrue();

    /**
     * 分页查询笔记（排除已删除）
     */
    @Query("SELECT n FROM Note n WHERE n.isDeleted = false")
    Page<Note> findAllActive(Pageable pageable);

    /**
     * 根据学生分页查询笔记
     */
    @Query("SELECT n FROM Note n WHERE n.isDeleted = false AND n.studentId = :studentId")
    Page<Note> findByStudentIdAndNotDeleted(@Param("studentId") Long studentId, Pageable pageable);

    /**
     * 根据关键词搜索笔记
     */
    @Query("SELECT n FROM Note n WHERE n.isDeleted = false AND " +
           "(n.title LIKE %:keyword% OR n.content LIKE %:keyword% OR n.tags LIKE %:keyword%)")
    Page<Note> searchNotes(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据学生和关键词搜索笔记
     */
    @Query("SELECT n FROM Note n WHERE n.isDeleted = false AND n.studentId = :studentId AND " +
           "(n.title LIKE %:keyword% OR n.content LIKE %:keyword% OR n.tags LIKE %:keyword%)")
    Page<Note> searchNotesByStudent(@Param("studentId") Long studentId, @Param("keyword") String keyword, Pageable pageable);

    /**
     * 统计学生笔记数量
     */
    @Query("SELECT COUNT(n) FROM Note n WHERE n.isDeleted = false AND n.studentId = :studentId")
    Long countByStudentId(@Param("studentId") Long studentId);
}
