<template>
  <div class="grades-page">
    <!-- 头部导航 -->
    <van-nav-bar title="我的成绩" left-arrow @click-left="$router.go(-1)">
      <template #right>
        <van-icon name="chart-trending-o" @click="showAnalysisPopup = true" />
      </template>
    </van-nav-bar>

    <!-- 成绩概览 -->
    <div class="overview-section">
      <van-row gutter="12">
        <van-col span="8">
          <div class="overview-card">
            <div class="overview-number">{{ stats.totalExams }}</div>
            <div class="overview-label">总考试</div>
          </div>
        </van-col>
        <van-col span="8">
          <div class="overview-card">
            <div class="overview-number">{{ stats.averageScore }}%</div>
            <div class="overview-label">平均分</div>
          </div>
        </van-col>
        <van-col span="8">
          <div class="overview-card">
            <div class="overview-number">{{ stats.bestSubject }}</div>
            <div class="overview-label">优势学科</div>
          </div>
        </van-col>
      </van-row>
    </div>

    <!-- 成绩趋势图 -->
    <div class="trend-section">
      <van-cell-group>
        <van-cell title="成绩趋势" is-link @click="showTrendChart = !showTrendChart">
          <template #right-icon>
            <van-icon :name="showTrendChart ? 'arrow-up' : 'arrow-down'" />
          </template>
        </van-cell>
      </van-cell-group>

      <div v-show="showTrendChart" class="chart-container">
        <div ref="trendChartRef" class="trend-chart"></div>
      </div>
    </div>

    <!-- 学科筛选 -->
    <van-tabs v-model:active="activeSubject" @change="onSubjectChange" sticky>
      <van-tab title="全部" name="all"></van-tab>
      <van-tab
        v-for="subject in subjects"
        :key="subject.id"
        :title="subject.name"
        :name="subject.id"
      ></van-tab>
    </van-tabs>

    <!-- 成绩列表 -->
    <div class="grades-list">
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-model:loading="loading"
          :finished="finished"
          finished-text="没有更多了"
          @load="onLoad"
        >
          <div
            v-for="grade in grades"
            :key="grade.id"
            class="grade-item"
            @click="viewGradeDetail(grade)"
          >
            <div class="grade-header">
              <div class="exam-info">
                <h3 class="exam-name">{{ grade.examName }}</h3>
                <p class="exam-type">{{ grade.examType }}</p>
              </div>
              <div class="grade-score">
                <span class="score">{{ grade.score }}</span>
                <span class="total-score">/ {{ grade.totalScore }}</span>
              </div>
            </div>

            <div class="grade-details">
              <div class="detail-row">
                <span class="detail-label">学科：</span>
                <span class="detail-value">{{ grade.subjectName }}</span>
              </div>
              <div class="detail-row">
                <span class="detail-label">得分率：</span>
                <span class="detail-value" :class="getScoreRateClass(grade.scoreRate)">
                  {{ grade.scoreRate }}%
                </span>
              </div>
              <div class="detail-row" v-if="grade.classRank">
                <span class="detail-label">班级排名：</span>
                <span class="detail-value">{{ grade.classRank }}</span>
              </div>
              <div class="detail-row" v-if="grade.gradeRank">
                <span class="detail-label">年级排名：</span>
                <span class="detail-value">{{ grade.gradeRank }}</span>
              </div>
            </div>

            <div class="grade-footer">
              <span class="exam-date">{{ formatDate(grade.examDate) }}</span>
              <van-tag :type="getStatusColor(grade.status)">
                {{ getStatusText(grade.status) }}
              </van-tag>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>

    <!-- 空状态 -->
    <van-empty
      v-if="!loading && grades.length === 0"
      description="暂无成绩记录"
      image="search"
    >
      <van-button type="primary" @click="$router.push('/practice')">
        去练习
      </van-button>
    </van-empty>

    <!-- 成绩详情弹窗 -->
    <van-popup v-model:show="showDetailPopup" position="bottom" style="height: 80%">
      <div class="detail-popup" v-if="currentGrade">
        <div class="detail-header">
          <van-nav-bar :title="currentGrade.examName" @click-left="showDetailPopup = false">
            <template #left>
              <van-icon name="cross" />
            </template>
          </van-nav-bar>
        </div>

        <div class="detail-content">
          <div class="score-display">
            <div class="main-score">
              <span class="score-number">{{ currentGrade.score }}</span>
              <span class="score-total">/ {{ currentGrade.totalScore }}</span>
            </div>
            <div class="score-rate" :class="getScoreRateClass(currentGrade.scoreRate)">
              {{ currentGrade.scoreRate }}%
            </div>
          </div>

          <van-cell-group>
            <van-cell title="考试类型" :value="currentGrade.examType" />
            <van-cell title="学科" :value="currentGrade.subjectName" />
            <van-cell title="考试时间" :value="formatDate(currentGrade.examDate)" />
            <van-cell title="班级排名" :value="currentGrade.classRank || '暂无'" />
            <van-cell title="年级排名" :value="currentGrade.gradeRank || '暂无'" />
            <van-cell title="状态">
              <template #value>
                <van-tag :type="getStatusColor(currentGrade.status)">
                  {{ getStatusText(currentGrade.status) }}
                </van-tag>
              </template>
            </van-cell>
          </van-cell-group>

          <div class="comments-section" v-if="currentGrade.comments">
            <h4>老师评语</h4>
            <p class="comments-text">{{ currentGrade.comments }}</p>
          </div>

          <div class="analysis-section">
            <h4>成绩分析</h4>
            <div class="analysis-item">
              <span class="analysis-label">与班级平均分差距：</span>
              <span class="analysis-value" :class="currentGrade.classAvgDiff >= 0 ? 'positive' : 'negative'">
                {{ currentGrade.classAvgDiff >= 0 ? '+' : '' }}{{ currentGrade.classAvgDiff }}分
              </span>
            </div>
            <div class="analysis-item">
              <span class="analysis-label">与年级平均分差距：</span>
              <span class="analysis-value" :class="currentGrade.gradeAvgDiff >= 0 ? 'positive' : 'negative'">
                {{ currentGrade.gradeAvgDiff >= 0 ? '+' : '' }}{{ currentGrade.gradeAvgDiff }}分
              </span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>

    <!-- 成绩分析弹窗 -->
    <van-popup v-model:show="showAnalysisPopup" position="bottom" style="height: 85%">
      <div class="analysis-popup">
        <div class="analysis-header">
          <van-nav-bar title="成绩分析" @click-left="showAnalysisPopup = false">
            <template #left>
              <van-icon name="cross" />
            </template>
          </van-nav-bar>
        </div>

        <div class="analysis-content">
          <div class="analysis-summary">
            <h3>学习表现总结</h3>
            <div class="summary-cards">
              <div class="summary-card">
                <div class="card-title">最近进步</div>
                <div class="card-value positive">+{{ recentProgress }}分</div>
              </div>
              <div class="summary-card">
                <div class="card-title">稳定性</div>
                <div class="card-value">{{ stability }}</div>
              </div>
              <div class="summary-card">
                <div class="card-title">目标达成</div>
                <div class="card-value">{{ goalAchievement }}%</div>
              </div>
            </div>
          </div>

          <div class="subject-analysis">
            <h3>各学科表现</h3>
            <div class="subject-list">
              <div
                v-for="subject in subjectAnalysis"
                :key="subject.id"
                class="subject-item"
              >
                <div class="subject-name">{{ subject.name }}</div>
                <div class="subject-score">{{ subject.averageScore }}分</div>
                <div class="subject-trend" :class="subject.trend">
                  <van-icon :name="subject.trend === 'up' ? 'arrow-up' : subject.trend === 'down' ? 'arrow-down' : 'minus'" />
                  {{ subject.trendText }}
                </div>
              </div>
            </div>
          </div>

          <div class="suggestions">
            <h3>学习建议</h3>
            <div class="suggestion-list">
              <div
                v-for="(suggestion, index) in suggestions"
                :key="index"
                class="suggestion-item"
              >
                <van-icon name="bulb-o" />
                <span>{{ suggestion }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { getMyGrades, getMyGradeStats, getGradeTrend, getSubjectGradeAnalysis } from '@/api/grades'
import { getSubjects } from '@/api/subjects'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const refreshing = ref(false)
const finished = ref(false)
const showDetailPopup = ref(false)
const showAnalysisPopup = ref(false)
const showTrendChart = ref(false)

const grades = ref([])
const subjects = ref([])
const currentGrade = ref(null)
const activeSubject = ref('all')

// 图表引用
const trendChartRef = ref()

// 统计数据
const stats = reactive({
  totalExams: 0,
  averageScore: 0,
  bestSubject: '暂无'
})

// 分页参数
const pagination = reactive({
  page: 0,
  size: 20,
  hasMore: true
})

// 分析数据
const recentProgress = ref(0)
const stability = ref('良好')
const goalAchievement = ref(0)
const subjectAnalysis = ref([])
const suggestions = ref([])

// 方法
const loadGrades = async (isRefresh = false) => {
  if (loading.value) return

  loading.value = true

  try {
    if (isRefresh) {
      pagination.page = 0
      pagination.hasMore = true
      finished.value = false
    }

    const params = {
      page: pagination.page,
      size: pagination.size,
      subjectId: activeSubject.value === 'all' ? null : activeSubject.value
    }

    const response = await getMyGrades(params)
    const newData = response.data.content || response.data

    // 计算得分率
    newData.forEach(grade => {
      grade.scoreRate = grade.totalScore > 0 ? Math.round((grade.score / grade.totalScore) * 100) : 0
    })

    if (isRefresh) {
      grades.value = newData
    } else {
      grades.value.push(...newData)
    }

    pagination.page++

    if (newData.length < pagination.size) {
      finished.value = true
      pagination.hasMore = false
    }

  } catch (error) {
    showToast('加载成绩失败')
  } finally {
    loading.value = false
    refreshing.value = false
  }
}

const loadStats = async () => {
  try {
    const response = await getMyGradeStats()
    Object.assign(stats, response.data)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadSubjects = async () => {
  try {
    const response = await getSubjects()
    subjects.value = response.data
  } catch (error) {
    console.error('加载学科列表失败:', error)
  }
}

const loadTrendData = async () => {
  try {
    const response = await getGradeTrend({ months: 6 })
    const trendData = response.data

    // 这里可以使用图表库（如ECharts）来渲染趋势图
    await nextTick()
    if (trendChartRef.value) {
      renderTrendChart(trendData)
    }
  } catch (error) {
    console.error('加载趋势数据失败:', error)
  }
}

const loadAnalysisData = async () => {
  try {
    // 加载各学科分析数据
    const analysisPromises = subjects.value.map(subject =>
      getSubjectGradeAnalysis(subject.id).catch(() => null)
    )

    const analysisResults = await Promise.all(analysisPromises)

    subjectAnalysis.value = subjects.value.map((subject, index) => {
      const analysis = analysisResults[index]
      return {
        id: subject.id,
        name: subject.name,
        averageScore: analysis?.averageScore || 0,
        trend: analysis?.trend || 'stable',
        trendText: getTrendText(analysis?.trend || 'stable')
      }
    }).filter(item => item.averageScore > 0)

    // 生成学习建议
    generateSuggestions()

  } catch (error) {
    console.error('加载分析数据失败:', error)
  }
}

const onLoad = () => {
  if (!pagination.hasMore) {
    finished.value = true
    return
  }
  loadGrades()
}

const onRefresh = () => {
  loadGrades(true)
}

const onSubjectChange = () => {
  grades.value = []
  pagination.page = 0
  pagination.hasMore = true
  finished.value = false
  loadGrades(true)
}

const viewGradeDetail = (grade) => {
  // 模拟详细数据
  currentGrade.value = {
    ...grade,
    classAvgDiff: Math.round((Math.random() - 0.5) * 20),
    gradeAvgDiff: Math.round((Math.random() - 0.5) * 15)
  }
  showDetailPopup.value = true
}

const renderTrendChart = (data) => {
  // 这里可以集成图表库来渲染趋势图
  // 例如使用ECharts、Chart.js等
  console.log('渲染趋势图:', data)
}

const generateSuggestions = () => {
  const suggestionPool = [
    '建议加强薄弱学科的练习，多做相关题目',
    '保持优势学科的学习状态，继续巩固知识点',
    '注意复习错题，避免重复犯错',
    '制定合理的学习计划，保证各学科均衡发展',
    '多参与课堂讨论，提高学习积极性',
    '定期总结学习方法，找到最适合自己的学习方式'
  ]

  suggestions.value = suggestionPool.slice(0, 3)
}

// 辅助方法
const getScoreRateClass = (rate) => {
  if (rate >= 90) return 'excellent'
  if (rate >= 80) return 'good'
  if (rate >= 70) return 'average'
  return 'poor'
}

const getStatusColor = (status) => {
  const map = {
    'PUBLISHED': 'success',
    'DRAFT': 'warning',
    'ARCHIVED': 'default'
  }
  return map[status] || 'default'
}

const getStatusText = (status) => {
  const map = {
    'PUBLISHED': '已发布',
    'DRAFT': '草稿',
    'ARCHIVED': '已归档'
  }
  return map[status] || status
}

const getTrendText = (trend) => {
  const map = {
    'up': '上升',
    'down': '下降',
    'stable': '稳定'
  }
  return map[trend] || '稳定'
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  loadStats()
  loadSubjects()
  loadGrades(true)

  // 延迟加载分析数据
  setTimeout(() => {
    loadAnalysisData()
    if (showTrendChart.value) {
      loadTrendData()
    }
  }, 1000)
})
</script>

<style scoped>
.grades-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

/* 成绩概览 */
.overview-section {
  padding: 16px;
  background: white;
  margin-bottom: 8px;
}

.overview-card {
  text-align: center;
  padding: 16px 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

.overview-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 12px;
  opacity: 0.9;
}

/* 趋势图 */
.trend-section {
  margin-bottom: 8px;
}

.chart-container {
  background: white;
  padding: 16px;
}

.trend-chart {
  height: 200px;
  width: 100%;
}

/* 成绩列表 */
.grades-list {
  padding: 0 16px;
}

.grade-item {
  background: white;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.grade-item:active {
  transform: scale(0.98);
}

.grade-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.exam-info {
  flex: 1;
}

.exam-name {
  font-size: 16px;
  font-weight: bold;
  color: #323233;
  margin: 0 0 4px 0;
}

.exam-type {
  font-size: 12px;
  color: #969799;
  margin: 0;
}

.grade-score {
  text-align: right;
  flex-shrink: 0;
}

.score {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
}

.total-score {
  font-size: 14px;
  color: #646566;
}

.grade-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
  font-size: 13px;
}

.detail-label {
  color: #646566;
}

.detail-value {
  color: #323233;
  font-weight: 500;
}

.detail-value.excellent {
  color: #07c160;
}

.detail-value.good {
  color: #1989fa;
}

.detail-value.average {
  color: #ff976a;
}

.detail-value.poor {
  color: #ee0a24;
}

.grade-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.exam-date {
  color: #969799;
}

/* 详情弹窗 */
.detail-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-content {
  flex: 1;
  overflow-y: auto;
}

.score-display {
  text-align: center;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.main-score {
  margin-bottom: 8px;
}

.score-number {
  font-size: 48px;
  font-weight: bold;
}

.score-total {
  font-size: 24px;
  opacity: 0.8;
}

.score-rate {
  font-size: 20px;
  font-weight: bold;
}

.score-rate.excellent {
  color: #4ade80;
}

.score-rate.good {
  color: #60a5fa;
}

.score-rate.average {
  color: #fbbf24;
}

.score-rate.poor {
  color: #f87171;
}

.comments-section {
  padding: 16px;
  background: white;
  margin: 8px 0;
}

.comments-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #323233;
}

.comments-text {
  font-size: 14px;
  line-height: 1.6;
  color: #646566;
  margin: 0;
}

.analysis-section {
  padding: 16px;
  background: white;
}

.analysis-section h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #323233;
}

.analysis-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.analysis-label {
  color: #646566;
}

.analysis-value {
  font-weight: bold;
}

.analysis-value.positive {
  color: #07c160;
}

.analysis-value.negative {
  color: #ee0a24;
}

/* 分析弹窗 */
.analysis-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.analysis-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.analysis-summary h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #323233;
}

.summary-cards {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.summary-card {
  flex: 1;
  text-align: center;
  padding: 16px;
  background: #f7f8fa;
  border-radius: 8px;
}

.card-title {
  font-size: 12px;
  color: #969799;
  margin-bottom: 8px;
}

.card-value {
  font-size: 20px;
  font-weight: bold;
  color: #323233;
}

.card-value.positive {
  color: #07c160;
}

.subject-analysis h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #323233;
}

.subject-list {
  margin-bottom: 24px;
}

.subject-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f7f8fa;
  border-radius: 8px;
  margin-bottom: 8px;
}

.subject-name {
  font-size: 14px;
  color: #323233;
  font-weight: 500;
}

.subject-score {
  font-size: 16px;
  font-weight: bold;
  color: #1989fa;
}

.subject-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.subject-trend.up {
  color: #07c160;
}

.subject-trend.down {
  color: #ee0a24;
}

.subject-trend.stable {
  color: #969799;
}

.suggestions h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  color: #323233;
}

.suggestion-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  color: #323233;
}

/* 平板适配 */
@media (min-width: 768px) {
  .grades-page {
    max-width: 768px;
    margin: 0 auto;
  }

  .overview-section {
    padding: 24px;
  }

  .overview-card {
    padding: 24px 16px;
  }

  .overview-number {
    font-size: 28px;
  }

  .overview-label {
    font-size: 14px;
  }

  .chart-container {
    padding: 24px;
  }

  .trend-chart {
    height: 250px;
  }

  .grades-list {
    padding: 0 24px;
  }

  .grade-item {
    padding: 20px;
    margin-bottom: 16px;
  }

  .exam-name {
    font-size: 18px;
  }

  .exam-type {
    font-size: 14px;
  }

  .score {
    font-size: 28px;
  }

  .total-score {
    font-size: 16px;
  }

  .detail-row {
    font-size: 15px;
  }

  .grade-footer {
    font-size: 14px;
  }

  .score-display {
    padding: 32px;
  }

  .score-number {
    font-size: 56px;
  }

  .score-total {
    font-size: 28px;
  }

  .score-rate {
    font-size: 24px;
  }

  .analysis-content {
    padding: 24px;
  }

  .summary-cards {
    gap: 16px;
  }

  .summary-card {
    padding: 20px;
  }

  .card-value {
    font-size: 24px;
  }
}

/* 大屏平板适配 */
@media (min-width: 1024px) {
  .grades-page {
    max-width: 1024px;
  }

  .overview-section .van-row {
    max-width: 600px;
    margin: 0 auto;
  }

  .grades-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 16px;
    padding: 0 24px;
  }

  .grade-item {
    margin-bottom: 0;
  }

  .summary-cards {
    max-width: 600px;
    margin: 0 auto 24px auto;
  }
}

/* 横屏适配 */
@media (orientation: landscape) and (max-height: 600px) {
  .detail-popup,
  .analysis-popup {
    height: 95vh;
  }

  .overview-section {
    padding: 12px 16px;
  }

  .overview-card {
    padding: 12px 8px;
  }

  .overview-number {
    font-size: 20px;
  }

  .score-display {
    padding: 16px;
  }

  .score-number {
    font-size: 36px;
  }

  .score-total {
    font-size: 18px;
  }

  .score-rate {
    font-size: 16px;
  }
}

/* 触摸优化 */
.van-button {
  min-height: 44px;
}

.grade-item {
  min-height: 44px;
}

.van-cell {
  min-height: 54px;
}

.subject-item {
  min-height: 44px;
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
  .grade-item {
    transition: none;
  }

  .grade-item:active {
    transform: none;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .grades-page {
    background-color: #1a1a1a;
  }

  .overview-section,
  .chart-container,
  .grade-item,
  .comments-section,
  .analysis-section {
    background: #2a2a2a;
    color: #ffffff;
  }

  .exam-name {
    color: #ffffff;
  }

  .detail-value {
    color: #ffffff;
  }

  .summary-card,
  .subject-item {
    background: #3a3a3a;
  }

  .suggestion-item {
    background: #2a3a4a;
    color: #ffffff;
  }
}
</style>
