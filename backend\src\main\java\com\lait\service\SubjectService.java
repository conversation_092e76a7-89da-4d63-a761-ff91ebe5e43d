package com.lait.service;

import com.lait.dto.SubjectDTO;
import com.lait.entity.Subject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * 学科服务接口
 */
public interface SubjectService {

    /**
     * 创建学科
     */
    SubjectDTO createSubject(SubjectDTO.CreateSubjectRequest request);

    /**
     * 根据ID获取学科
     */
    SubjectDTO getSubjectById(Long id);

    /**
     * 更新学科信息
     */
    SubjectDTO updateSubject(Long id, SubjectDTO.UpdateSubjectRequest request);

    /**
     * 删除学科
     */
    void deleteSubject(Long id);

    /**
     * 分页查询学科
     */
    Page<SubjectDTO> getSubjects(Pageable pageable);

    /**
     * 搜索学科
     */
    Page<SubjectDTO> searchSubjects(String keyword, Pageable pageable);

    /**
     * 根据年级获取学科
     */
    List<SubjectDTO> getSubjectsByGrade(Integer gradeLevel);

    /**
     * 获取所有启用的学科
     */
    List<SubjectDTO> getAllActiveSubjects();

    /**
     * 检查学科代码是否存在
     */
    boolean existsBySubjectCode(String subjectCode);

    /**
     * 启用学科
     */
    void activateSubject(Long id);

    /**
     * 禁用学科
     */
    void deactivateSubject(Long id);
}
