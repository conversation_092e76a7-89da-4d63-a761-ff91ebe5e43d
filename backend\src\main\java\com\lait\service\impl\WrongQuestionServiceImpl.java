package com.lait.service.impl;

import com.lait.dto.WrongQuestionDTO;
import com.lait.entity.Question;
import com.lait.entity.Subject;
import com.lait.entity.User;
import com.lait.entity.WrongQuestion;
import com.lait.repository.QuestionRepository;
import com.lait.repository.SubjectRepository;
import com.lait.repository.UserRepository;
import com.lait.repository.WrongQuestionRepository;
import com.lait.service.WrongQuestionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 错题服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WrongQuestionServiceImpl implements WrongQuestionService {

    private final WrongQuestionRepository wrongQuestionRepository;
    private final QuestionRepository questionRepository;
    private final SubjectRepository subjectRepository;
    private final UserRepository userRepository;

    @Override
    @Transactional
    public WrongQuestionDTO createWrongQuestion(WrongQuestionDTO.CreateWrongQuestionRequest request) {
        log.info("创建错题记录: 学生ID={}, 题目ID={}", request.getStudentId(), request.getQuestionId());

        // 验证学生是否存在
        User student = userRepository.findById(request.getStudentId())
                .orElseThrow(() -> new RuntimeException("学生不存在"));

        // 验证题目是否存在
        Question question = questionRepository.findById(request.getQuestionId())
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        // 检查是否已存在错题记录
        Optional<WrongQuestion> existingWrongQuestion = wrongQuestionRepository
                .findByStudentIdAndQuestionId(request.getStudentId(), request.getQuestionId());

        WrongQuestion wrongQuestion;
        if (existingWrongQuestion.isPresent()) {
            // 如果已存在，增加错误次数
            wrongQuestion = existingWrongQuestion.get();
            wrongQuestion.setWrongCount(wrongQuestion.getWrongCount() + 1);
            wrongQuestion.setStudentAnswer(request.getStudentAnswer());
            wrongQuestion.setReviewStatus(WrongQuestion.ReviewStatus.PENDING);
            wrongQuestion.setIsMastered(false);
        } else {
            // 创建新的错题记录
            wrongQuestion = new WrongQuestion();
            BeanUtils.copyProperties(request, wrongQuestion);
        }

        WrongQuestion savedWrongQuestion = wrongQuestionRepository.save(wrongQuestion);
        return convertToDTO(savedWrongQuestion);
    }

    @Override
    public WrongQuestionDTO getWrongQuestionById(Long id) {
        WrongQuestion wrongQuestion = wrongQuestionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("错题记录不存在"));
        return convertToDTO(wrongQuestion);
    }

    @Override
    @Transactional
    public WrongQuestionDTO updateWrongQuestion(Long id, WrongQuestionDTO.UpdateWrongQuestionRequest request) {
        log.info("更新错题记录: {}", id);

        WrongQuestion wrongQuestion = wrongQuestionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("错题记录不存在"));

        BeanUtils.copyProperties(request, wrongQuestion, "id", "studentId", "questionId", "wrongCount");
        WrongQuestion savedWrongQuestion = wrongQuestionRepository.save(wrongQuestion);
        return convertToDTO(savedWrongQuestion);
    }

    @Override
    @Transactional
    public void deleteWrongQuestion(Long id) {
        log.info("删除错题记录: {}", id);

        WrongQuestion wrongQuestion = wrongQuestionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("错题记录不存在"));

        wrongQuestion.setIsDeleted(true);
        wrongQuestionRepository.save(wrongQuestion);
    }

    @Override
    public Page<WrongQuestionDTO> getWrongQuestions(Pageable pageable) {
        Page<WrongQuestion> wrongQuestions = wrongQuestionRepository.findAllActive(pageable);
        return wrongQuestions.map(this::convertToDTO);
    }

    @Override
    public Page<WrongQuestionDTO> getWrongQuestionsByStudentId(Long studentId, Pageable pageable) {
        Page<WrongQuestion> wrongQuestions = wrongQuestionRepository.findByStudentIdAndNotDeleted(studentId, pageable);
        return wrongQuestions.map(this::convertToDTO);
    }

    @Override
    public Page<WrongQuestionDTO> searchWrongQuestions(WrongQuestionDTO.WrongQuestionQueryRequest request, Pageable pageable) {
        // 简化处理，实际应该在Repository中实现复杂查询
        if (request.getStudentId() != null) {
            if (request.getReviewStatus() != null) {
                return getWrongQuestionsByStatus(request.getStudentId(), request.getReviewStatus(), pageable);
            }
            return getWrongQuestionsByStudentId(request.getStudentId(), pageable);
        }
        return getWrongQuestions(pageable);
    }

    @Override
    public Page<WrongQuestionDTO> getWrongQuestionsByStatus(Long studentId, WrongQuestion.ReviewStatus status, Pageable pageable) {
        List<WrongQuestion> wrongQuestions = wrongQuestionRepository.findByStudentIdAndReviewStatus(studentId, status);
        // 这里简化处理，实际应该在Repository中实现分页查询
        return Page.empty(pageable);
    }

    @Override
    public List<WrongQuestionDTO> getPendingReviewQuestions(Long studentId, int limit) {
        List<WrongQuestion> wrongQuestions = wrongQuestionRepository
                .findByStudentIdAndReviewStatus(studentId, WrongQuestion.ReviewStatus.PENDING);
        return wrongQuestions.stream()
                .filter(wq -> !wq.getIsDeleted())
                .limit(limit)
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void markWrongQuestionMastered(WrongQuestionDTO.MarkMasteredRequest request) {
        WrongQuestion wrongQuestion = wrongQuestionRepository.findById(request.getWrongQuestionId())
                .orElseThrow(() -> new RuntimeException("错题记录不存在"));

        wrongQuestion.setIsMastered(request.getIsMastered());
        wrongQuestion.setReviewStatus(request.getIsMastered() ? 
                WrongQuestion.ReviewStatus.MASTERED : WrongQuestion.ReviewStatus.PENDING);
        
        if (request.getNotes() != null) {
            wrongQuestion.setNotes(request.getNotes());
        }

        wrongQuestionRepository.save(wrongQuestion);
    }

    @Override
    @Transactional
    public void batchMarkWrongQuestionsMastered(WrongQuestionDTO.BatchMarkMasteredRequest request) {
        for (Long wrongQuestionId : request.getWrongQuestionIds()) {
            WrongQuestionDTO.MarkMasteredRequest markRequest = new WrongQuestionDTO.MarkMasteredRequest();
            markRequest.setWrongQuestionId(wrongQuestionId);
            markRequest.setIsMastered(request.getIsMastered());
            markWrongQuestionMastered(markRequest);
        }
    }

    @Override
    @Transactional
    public void incrementWrongCount(Long studentId, Long questionId, String studentAnswer) {
        Optional<WrongQuestion> existingWrongQuestion = wrongQuestionRepository
                .findByStudentIdAndQuestionId(studentId, questionId);

        if (existingWrongQuestion.isPresent()) {
            WrongQuestion wrongQuestion = existingWrongQuestion.get();
            wrongQuestion.setWrongCount(wrongQuestion.getWrongCount() + 1);
            wrongQuestion.setStudentAnswer(studentAnswer);
            wrongQuestion.setReviewStatus(WrongQuestion.ReviewStatus.PENDING);
            wrongQuestion.setIsMastered(false);
            wrongQuestionRepository.save(wrongQuestion);
        } else {
            // 创建新的错题记录
            WrongQuestionDTO.CreateWrongQuestionRequest request = new WrongQuestionDTO.CreateWrongQuestionRequest();
            request.setStudentId(studentId);
            request.setQuestionId(questionId);
            request.setStudentAnswer(studentAnswer);
            createWrongQuestion(request);
        }
    }

    @Override
    public WrongQuestionDTO.WrongQuestionStatistics getStudentWrongQuestionStatistics(Long studentId) {
        User student = userRepository.findById(studentId)
                .orElseThrow(() -> new RuntimeException("学生不存在"));

        List<WrongQuestion> wrongQuestions = wrongQuestionRepository.findByStudentId(studentId)
                .stream()
                .filter(wq -> !wq.getIsDeleted())
                .collect(Collectors.toList());

        WrongQuestionDTO.WrongQuestionStatistics statistics = new WrongQuestionDTO.WrongQuestionStatistics();
        statistics.setStudentId(studentId);
        statistics.setStudentName(student.getRealName());
        statistics.setTotalWrongQuestions(wrongQuestions.size());

        // 统计各状态的错题数量
        Map<WrongQuestion.ReviewStatus, Long> statusCount = wrongQuestions.stream()
                .collect(Collectors.groupingBy(WrongQuestion::getReviewStatus, Collectors.counting()));

        statistics.setPendingReview(statusCount.getOrDefault(WrongQuestion.ReviewStatus.PENDING, 0L).intValue());
        statistics.setReviewing(statusCount.getOrDefault(WrongQuestion.ReviewStatus.REVIEWING, 0L).intValue());
        statistics.setMastered(statusCount.getOrDefault(WrongQuestion.ReviewStatus.MASTERED, 0L).intValue());

        // 计算掌握率
        if (wrongQuestions.size() > 0) {
            double masteryRate = (double) statistics.getMastered() / wrongQuestions.size() * 100;
            statistics.setMasteryRate(masteryRate);
        } else {
            statistics.setMasteryRate(0.0);
        }

        // 统计最近一周的错题数量
        LocalDateTime oneWeekAgo = LocalDateTime.now().minusWeeks(1);
        int recentWrongCount = (int) wrongQuestions.stream()
                .filter(wq -> wq.getCreatedTime().isAfter(oneWeekAgo))
                .count();
        statistics.setRecentWrongCount(recentWrongCount);

        return statistics;
    }

    @Override
    public List<WrongQuestionDTO.SubjectWrongStatistics> getSubjectWrongStatistics(Long studentId) {
        List<WrongQuestion> wrongQuestions = wrongQuestionRepository.findByStudentId(studentId)
                .stream()
                .filter(wq -> !wq.getIsDeleted())
                .collect(Collectors.toList());

        // 按学科分组统计
        Map<Long, List<WrongQuestion>> subjectGroups = wrongQuestions.stream()
                .collect(Collectors.groupingBy(wq -> {
                    Question question = questionRepository.findById(wq.getQuestionId()).orElse(null);
                    return question != null ? question.getSubjectId() : 0L;
                }));

        return subjectGroups.entrySet().stream()
                .filter(entry -> entry.getKey() != 0L)
                .map(entry -> {
                    Long subjectId = entry.getKey();
                    List<WrongQuestion> subjectWrongQuestions = entry.getValue();

                    WrongQuestionDTO.SubjectWrongStatistics statistics = new WrongQuestionDTO.SubjectWrongStatistics();
                    statistics.setSubjectId(subjectId);
                    statistics.setWrongCount(subjectWrongQuestions.size());
                    statistics.setMasteredCount((int) subjectWrongQuestions.stream()
                            .filter(WrongQuestion::getIsMastered)
                            .count());

                    if (subjectWrongQuestions.size() > 0) {
                        double masteryRate = (double) statistics.getMasteredCount() / subjectWrongQuestions.size() * 100;
                        statistics.setMasteryRate(masteryRate);
                    } else {
                        statistics.setMasteryRate(0.0);
                    }

                    // 设置学科名称
                    subjectRepository.findById(subjectId)
                            .ifPresent(subject -> statistics.setSubjectName(subject.getName()));

                    return statistics;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getMostWrongQuestionTypes(Long studentId, Long subjectId) {
        List<WrongQuestion> wrongQuestions = wrongQuestionRepository.findByStudentId(studentId)
                .stream()
                .filter(wq -> !wq.getIsDeleted())
                .collect(Collectors.toList());

        // 统计题目类型
        Map<String, Long> typeCount = wrongQuestions.stream()
                .map(wq -> questionRepository.findById(wq.getQuestionId()).orElse(null))
                .filter(question -> question != null && question.getSubjectId().equals(subjectId))
                .collect(Collectors.groupingBy(
                        question -> question.getQuestionType().getDescription(),
                        Collectors.counting()
                ));

        return typeCount.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e2.getValue(), e1.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    @Override
    public List<WrongQuestionDTO> getReviewSuggestions(Long studentId, int limit) {
        // 优先推荐错误次数多且未掌握的题目
        List<WrongQuestion> wrongQuestions = wrongQuestionRepository.findByStudentId(studentId)
                .stream()
                .filter(wq -> !wq.getIsDeleted() && !wq.getIsMastered())
                .sorted((wq1, wq2) -> Integer.compare(wq2.getWrongCount(), wq1.getWrongCount()))
                .limit(limit)
                .collect(Collectors.toList());

        return wrongQuestions.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void resetWrongQuestionStatus(Long wrongQuestionId) {
        WrongQuestion wrongQuestion = wrongQuestionRepository.findById(wrongQuestionId)
                .orElseThrow(() -> new RuntimeException("错题记录不存在"));

        wrongQuestion.setReviewStatus(WrongQuestion.ReviewStatus.PENDING);
        wrongQuestion.setIsMastered(false);
        wrongQuestionRepository.save(wrongQuestion);
    }

    @Override
    @Transactional
    public void batchDeleteWrongQuestions(Long[] wrongQuestionIds) {
        for (Long wrongQuestionId : wrongQuestionIds) {
            deleteWrongQuestion(wrongQuestionId);
        }
    }

    @Override
    public boolean existsWrongQuestion(Long studentId, Long questionId) {
        return wrongQuestionRepository.findByStudentIdAndQuestionId(studentId, questionId).isPresent();
    }

    @Override
    public Double getWrongQuestionMasteryRate(Long studentId, Long subjectId) {
        List<WrongQuestion> wrongQuestions = wrongQuestionRepository.findByStudentId(studentId)
                .stream()
                .filter(wq -> !wq.getIsDeleted())
                .filter(wq -> {
                    Question question = questionRepository.findById(wq.getQuestionId()).orElse(null);
                    return question != null && question.getSubjectId().equals(subjectId);
                })
                .collect(Collectors.toList());

        if (wrongQuestions.isEmpty()) {
            return 100.0; // 没有错题，掌握率为100%
        }

        long masteredCount = wrongQuestions.stream()
                .filter(WrongQuestion::getIsMastered)
                .count();

        return (double) masteredCount / wrongQuestions.size() * 100;
    }

    @Override
    public List<WrongQuestionDTO> getRecentWrongQuestions(Long studentId, int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdTime"));
        Page<WrongQuestion> wrongQuestions = wrongQuestionRepository.findByStudentIdAndNotDeleted(studentId, pageable);
        return wrongQuestions.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    /**
     * 转换为DTO
     */
    private WrongQuestionDTO convertToDTO(WrongQuestion wrongQuestion) {
        WrongQuestionDTO dto = new WrongQuestionDTO();
        BeanUtils.copyProperties(wrongQuestion, dto);

        // 设置学生名称
        userRepository.findById(wrongQuestion.getStudentId())
                .ifPresent(student -> dto.setStudentName(student.getRealName()));

        // 设置题目信息
        questionRepository.findById(wrongQuestion.getQuestionId())
                .ifPresent(question -> {
                    dto.setQuestionTitle(question.getContent());
                    dto.setQuestionContent(question.getContent());
                    dto.setCorrectAnswer(question.getCorrectAnswer());

                    // 设置学科名称
                    subjectRepository.findById(question.getSubjectId())
                            .ifPresent(subject -> dto.setSubjectName(subject.getName()));
                });

        return dto;
    }
}
