package com.lait.repository;

import com.lait.entity.UserPoints;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户积分记录数据访问层
 */
@Repository
public interface UserPointsRepository extends JpaRepository<UserPoints, Long> {

    /**
     * 根据用户ID查找积分记录
     */
    List<UserPoints> findByUserIdOrderByCreatedTimeDesc(Long userId);

    /**
     * 分页查询用户积分记录
     */
    Page<UserPoints> findByUserIdOrderByCreatedTimeDesc(Long userId, Pageable pageable);

    /**
     * 根据积分类型查找记录
     */
    List<UserPoints> findByPointsType(UserPoints.PointsType pointsType);

    /**
     * 查找用户最新的积分记录
     */
    @Query("SELECT p FROM UserPoints p WHERE p.userId = :userId ORDER BY p.createdTime DESC")
    List<UserPoints> findLatestByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 获取用户当前总积分
     */
    @Query("SELECT p.totalPoints FROM UserPoints p WHERE p.userId = :userId ORDER BY p.createdTime DESC")
    Integer getCurrentPointsByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 统计用户积分变化
     */
    @Query("SELECT SUM(p.pointsChange) FROM UserPoints p WHERE p.userId = :userId AND p.createdTime BETWEEN :startTime AND :endTime")
    Integer sumPointsChangeByUserIdAndTimeRange(@Param("userId") Long userId,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime);

    /**
     * 统计用户今日积分变化
     */
    @Query("SELECT SUM(p.pointsChange) FROM UserPoints p WHERE p.userId = :userId AND DATE(p.createdTime) = CURRENT_DATE")
    Integer sumTodayPointsChangeByUserId(@Param("userId") Long userId);

    /**
     * 分页查询积分记录
     */
    @Query("SELECT p FROM UserPoints p WHERE " +
           "(:userId IS NULL OR p.userId = :userId) AND " +
           "(:pointsType IS NULL OR p.pointsType = :pointsType) AND " +
           "(:source IS NULL OR p.source LIKE %:source%) AND " +
           "(:startTime IS NULL OR p.createdTime >= :startTime) AND " +
           "(:endTime IS NULL OR p.createdTime <= :endTime) " +
           "ORDER BY p.createdTime DESC")
    Page<UserPoints> findPointsWithFilters(@Param("userId") Long userId,
                                          @Param("pointsType") UserPoints.PointsType pointsType,
                                          @Param("source") String source,
                                          @Param("startTime") LocalDateTime startTime,
                                          @Param("endTime") LocalDateTime endTime,
                                          Pageable pageable);

    /**
     * 统计各类型积分记录数量
     */
    @Query("SELECT p.pointsType, COUNT(p) FROM UserPoints p GROUP BY p.pointsType")
    List<Object[]> countByPointsType();

    /**
     * 统计各来源积分记录数量
     */
    @Query("SELECT p.source, COUNT(p), SUM(p.pointsChange) FROM UserPoints p GROUP BY p.source")
    List<Object[]> countAndSumBySource();

    /**
     * 获取积分排行榜
     */
    @Query("SELECT p.userId, p.totalPoints FROM UserPoints p1 " +
           "WHERE p1.createdTime = (SELECT MAX(p2.createdTime) FROM UserPoints p2 WHERE p2.userId = p1.userId) " +
           "ORDER BY p1.totalPoints DESC")
    List<Object[]> getPointsRanking(Pageable pageable);

    /**
     * 统计用户积分趋势
     */
    @Query("SELECT DATE(p.createdTime), SUM(p.pointsChange) FROM UserPoints p " +
           "WHERE p.userId = :userId AND p.createdTime >= :startTime " +
           "GROUP BY DATE(p.createdTime) ORDER BY DATE(p.createdTime)")
    List<Object[]> getPointsTrendByUserId(@Param("userId") Long userId, @Param("startTime") LocalDateTime startTime);
}
