<template>
  <div class="questions-page">
    <div class="page-header">
      <h1>题目管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        添加题目
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="学科">
          <el-select v-model="searchForm.subjectId" placeholder="选择学科" clearable>
            <el-option
              v-for="subject in subjects"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题目类型">
          <el-select v-model="searchForm.questionType" placeholder="选择类型" clearable>
            <el-option label="单选题" value="SINGLE_CHOICE" />
            <el-option label="多选题" value="MULTIPLE_CHOICE" />
            <el-option label="判断题" value="TRUE_FALSE" />
            <el-option label="填空题" value="FILL_BLANK" />
            <el-option label="简答题" value="SHORT_ANSWER" />
          </el-select>
        </el-form-item>
        <el-form-item label="难度">
          <el-select v-model="searchForm.difficulty" placeholder="选择难度" clearable>
            <el-option label="简单" value="EASY" />
            <el-option label="中等" value="MEDIUM" />
            <el-option label="困难" value="HARD" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级">
          <el-select v-model="searchForm.gradeLevel" placeholder="选择年级" clearable>
            <el-option
              v-for="grade in [1,2,3,4,5,6]"
              :key="grade"
              :label="`${grade}年级`"
              :value="grade"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 题目列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="questions"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="content" label="题目内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="subjectName" label="学科" width="100" />
        <el-table-column prop="questionType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getQuestionTypeColor(row.questionType)">
              {{ getQuestionTypeText(row.questionType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="difficulty" label="难度" width="100">
          <template #default="{ row }">
            <el-tag :type="getDifficultyColor(row.difficulty)">
              {{ getDifficultyText(row.difficulty) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="gradeLevel" label="年级" width="80">
          <template #default="{ row }">
            {{ row.gradeLevel }}年级
          </template>
        </el-table-column>
        <el-table-column prop="usageCount" label="使用次数" width="100" />
        <el-table-column prop="correctCount" label="正确次数" width="100" />
        <el-table-column label="正确率" width="100">
          <template #default="{ row }">
            {{ row.usageCount > 0 ? Math.round((row.correctCount / row.usageCount) * 100) : 0 }}%
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewQuestion(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editQuestion(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteQuestion(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedQuestions.length > 0">
        <el-button type="danger" @click="batchDelete">
          批量删除 ({{ selectedQuestions.length }})
        </el-button>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑题目' : '添加题目'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="学科" prop="subjectId">
          <el-select v-model="form.subjectId" placeholder="选择学科">
            <el-option
              v-for="subject in subjects"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="题目内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="4"
            placeholder="请输入题目内容"
          />
        </el-form-item>
        <el-form-item label="题目类型" prop="questionType">
          <el-select v-model="form.questionType" placeholder="选择类型">
            <el-option label="单选题" value="SINGLE_CHOICE" />
            <el-option label="多选题" value="MULTIPLE_CHOICE" />
            <el-option label="判断题" value="TRUE_FALSE" />
            <el-option label="填空题" value="FILL_BLANK" />
            <el-option label="简答题" value="SHORT_ANSWER" />
          </el-select>
        </el-form-item>
        <el-form-item label="选项" v-if="needOptions">
          <el-input
            v-model="form.options"
            type="textarea"
            :rows="3"
            placeholder="请输入选项，每行一个选项"
          />
        </el-form-item>
        <el-form-item label="正确答案" prop="correctAnswer">
          <el-input
            v-model="form.correctAnswer"
            placeholder="请输入正确答案"
          />
        </el-form-item>
        <el-form-item label="解析">
          <el-input
            v-model="form.explanation"
            type="textarea"
            :rows="3"
            placeholder="请输入题目解析"
          />
        </el-form-item>
        <el-form-item label="难度" prop="difficulty">
          <el-select v-model="form.difficulty" placeholder="选择难度">
            <el-option label="简单" value="EASY" />
            <el-option label="中等" value="MEDIUM" />
            <el-option label="困难" value="HARD" />
          </el-select>
        </el-form-item>
        <el-form-item label="年级" prop="gradeLevel">
          <el-select v-model="form.gradeLevel" placeholder="选择年级">
            <el-option
              v-for="grade in [1,2,3,4,5,6]"
              :key="grade"
              :label="`${grade}年级`"
              :value="grade"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-input
            v-model="form.tags"
            placeholder="请输入标签，用逗号分隔"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getQuestions, createQuestion, updateQuestion, deleteQuestion as deleteQuestionApi, batchDeleteQuestions } from '@/api/questions'
import { getSubjects } from '@/api/subjects'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const questions = ref([])
const subjects = ref([])
const selectedQuestions = ref([])

// 搜索表单
const searchForm = reactive({
  subjectId: '',
  questionType: '',
  difficulty: '',
  gradeLevel: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  subjectId: '',
  content: '',
  questionType: '',
  options: '',
  correctAnswer: '',
  explanation: '',
  difficulty: '',
  gradeLevel: '',
  tags: ''
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  subjectId: [{ required: true, message: '请选择学科', trigger: 'change' }],
  content: [{ required: true, message: '请输入题目内容', trigger: 'blur' }],
  questionType: [{ required: true, message: '请选择题目类型', trigger: 'change' }],
  correctAnswer: [{ required: true, message: '请输入正确答案', trigger: 'blur' }],
  difficulty: [{ required: true, message: '请选择难度', trigger: 'change' }],
  gradeLevel: [{ required: true, message: '请选择年级', trigger: 'change' }]
}

// 计算属性
const needOptions = computed(() => {
  return ['SINGLE_CHOICE', 'MULTIPLE_CHOICE'].includes(form.questionType)
})

// 方法
const loadQuestions = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page - 1,
      size: pagination.size,
      ...searchForm
    }
    const response = await getQuestions(params)
    questions.value = response.data.content
    pagination.total = response.data.totalElements
  } catch (error) {
    ElMessage.error('加载题目列表失败')
  } finally {
    loading.value = false
  }
}

const loadSubjects = async () => {
  try {
    const response = await getSubjects()
    subjects.value = response.data
  } catch (error) {
    ElMessage.error('加载学科列表失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadQuestions()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    subjectId: '',
    questionType: '',
    difficulty: '',
    gradeLevel: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadQuestions()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadQuestions()
}

const handleSelectionChange = (selection) => {
  selectedQuestions.value = selection
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const viewQuestion = (row) => {
  // 实现查看题目详情
  ElMessage.info('查看功能待实现')
}

const editQuestion = (row) => {
  isEdit.value = true
  Object.assign(form, {
    ...row,
    subjectId: row.subjectId || row.subject?.id
  })
  dialogVisible.value = true
}

const deleteQuestion = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这道题目吗？', '确认删除', {
      type: 'warning'
    })
    await deleteQuestionApi(row.id)
    ElMessage.success('删除成功')
    loadQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedQuestions.value.length} 道题目吗？`, '确认批量删除', {
      type: 'warning'
    })
    const ids = selectedQuestions.value.map(item => item.id)
    await batchDeleteQuestions(ids)
    ElMessage.success('批量删除成功')
    selectedQuestions.value = []
    loadQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    const data = { ...form }
    if (isEdit.value) {
      await updateQuestion(form.id, data)
      ElMessage.success('更新成功')
    } else {
      await createQuestion(data)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadQuestions()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    subjectId: '',
    content: '',
    questionType: '',
    options: '',
    correctAnswer: '',
    explanation: '',
    difficulty: '',
    gradeLevel: '',
    tags: ''
  })
  formRef.value?.resetFields()
}

// 辅助方法
const getQuestionTypeText = (type) => {
  const map = {
    'SINGLE_CHOICE': '单选题',
    'MULTIPLE_CHOICE': '多选题',
    'TRUE_FALSE': '判断题',
    'FILL_BLANK': '填空题',
    'SHORT_ANSWER': '简答题'
  }
  return map[type] || type
}

const getQuestionTypeColor = (type) => {
  const map = {
    'SINGLE_CHOICE': 'primary',
    'MULTIPLE_CHOICE': 'success',
    'TRUE_FALSE': 'warning',
    'FILL_BLANK': 'info',
    'SHORT_ANSWER': 'danger'
  }
  return map[type] || ''
}

const getDifficultyText = (difficulty) => {
  const map = {
    'EASY': '简单',
    'MEDIUM': '中等',
    'HARD': '困难'
  }
  return map[difficulty] || difficulty
}

const getDifficultyColor = (difficulty) => {
  const map = {
    'EASY': 'success',
    'MEDIUM': 'warning',
    'HARD': 'danger'
  }
  return map[difficulty] || ''
}

// 生命周期
onMounted(() => {
  loadQuestions()
  loadSubjects()
})
</script>

<style scoped>
.questions-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.batch-actions {
  margin: 20px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.el-form--inline .el-form-item {
  margin-right: 20px;
}

.el-table .el-table__cell {
  padding: 12px 0;
}

.el-dialog .el-form {
  padding: 0 20px;
}

.el-dialog .el-form-item {
  margin-bottom: 20px;
}

.el-tag {
  margin-right: 5px;
}

.el-button + .el-button {
  margin-left: 8px;
}

@media (max-width: 768px) {
  .questions-page {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
