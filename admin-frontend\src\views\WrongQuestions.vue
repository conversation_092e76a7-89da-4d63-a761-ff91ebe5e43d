<template>
  <div class="wrong-questions-page">
    <div class="page-header">
      <h1>错题管理</h1>
      <el-button type="primary" @click="showAnalysisDialog">
        <el-icon><TrendCharts /></el-icon>
        错题分析
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="学生">
          <el-select v-model="searchForm.studentId" placeholder="选择学生" clearable filterable>
            <el-option
              v-for="student in students"
              :key="student.id"
              :label="student.realName"
              :value="student.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学科">
          <el-select v-model="searchForm.subjectId" placeholder="选择学科" clearable>
            <el-option
              v-for="subject in subjects"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="复习状态">
          <el-select v-model="searchForm.reviewStatus" placeholder="选择状态" clearable>
            <el-option label="待复习" value="PENDING" />
            <el-option label="复习中" value="REVIEWING" />
            <el-option label="已掌握" value="MASTERED" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否掌握">
          <el-select v-model="searchForm.isMastered" placeholder="选择" clearable>
            <el-option label="已掌握" :value="true" />
            <el-option label="未掌握" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 错题列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="wrongQuestions"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="studentName" label="学生姓名" width="120" />
        <el-table-column prop="questionContent" label="题目内容" min-width="200" show-overflow-tooltip />
        <el-table-column prop="subjectName" label="学科" width="100" />
        <el-table-column prop="studentAnswer" label="学生答案" width="150" show-overflow-tooltip />
        <el-table-column prop="correctAnswer" label="正确答案" width="150" show-overflow-tooltip />
        <el-table-column prop="wrongCount" label="错误次数" width="100" />
        <el-table-column prop="reviewStatus" label="复习状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getReviewStatusColor(row.reviewStatus)">
              {{ getReviewStatusText(row.reviewStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isMastered" label="是否掌握" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isMastered ? 'success' : 'danger'">
              {{ row.isMastered ? '已掌握' : '未掌握' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="错误时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewWrongQuestion(row)">查看</el-button>
            <el-button
              size="small"
              type="success"
              @click="markAsMastered(row)"
              v-if="!row.isMastered"
            >
              标记掌握
            </el-button>
            <el-button size="small" type="danger" @click="deleteWrongQuestion(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedWrongQuestions.length > 0">
        <el-button type="success" @click="batchMarkAsMastered">
          批量标记掌握 ({{ selectedWrongQuestions.length }})
        </el-button>
        <el-button type="danger" @click="batchDelete">
          批量删除 ({{ selectedWrongQuestions.length }})
        </el-button>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 错题详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="错题详情"
      width="800px"
    >
      <div v-if="currentWrongQuestion" class="wrong-question-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="学生姓名">
            {{ currentWrongQuestion.studentName }}
          </el-descriptions-item>
          <el-descriptions-item label="学科">
            {{ currentWrongQuestion.subjectName }}
          </el-descriptions-item>
          <el-descriptions-item label="错误次数">
            {{ currentWrongQuestion.wrongCount }}
          </el-descriptions-item>
          <el-descriptions-item label="复习状态">
            <el-tag :type="getReviewStatusColor(currentWrongQuestion.reviewStatus)">
              {{ getReviewStatusText(currentWrongQuestion.reviewStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否掌握">
            <el-tag :type="currentWrongQuestion.isMastered ? 'success' : 'danger'">
              {{ currentWrongQuestion.isMastered ? '已掌握' : '未掌握' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="错误时间">
            {{ currentWrongQuestion.createdTime }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="question-content">
          <h4>题目内容</h4>
          <p>{{ currentWrongQuestion.questionContent }}</p>
        </div>

        <div class="answers">
          <el-row :gutter="20">
            <el-col :span="12">
              <h4>学生答案</h4>
              <p class="student-answer">{{ currentWrongQuestion.studentAnswer }}</p>
            </el-col>
            <el-col :span="12">
              <h4>正确答案</h4>
              <p class="correct-answer">{{ currentWrongQuestion.correctAnswer }}</p>
            </el-col>
          </el-row>
        </div>

        <div class="explanation" v-if="currentWrongQuestion.explanation">
          <h4>题目解析</h4>
          <p>{{ currentWrongQuestion.explanation }}</p>
        </div>

        <div class="notes" v-if="currentWrongQuestion.notes">
          <h4>学生笔记</h4>
          <p>{{ currentWrongQuestion.notes }}</p>
        </div>
      </div>
    </el-dialog>

    <!-- 错题分析对话框 -->
    <el-dialog
      v-model="analysisDialogVisible"
      title="错题分析报告"
      width="1000px"
    >
      <div class="analysis-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card>
              <template #header>
                <span>总体统计</span>
              </template>
              <div class="stat-item">
                <span class="stat-label">总错题数：</span>
                <span class="stat-value">{{ analysisData.totalCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">已掌握：</span>
                <span class="stat-value success">{{ analysisData.masteredCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">未掌握：</span>
                <span class="stat-value danger">{{ analysisData.unmasteredCount }}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card>
              <template #header>
                <span>学科分布</span>
              </template>
              <div class="chart-container" ref="subjectChartRef"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getWrongQuestions, deleteWrongQuestion as deleteWrongQuestionApi, batchDeleteWrongQuestions, markAsMastered as markAsMasteredApi, getWrongQuestionAnalysis } from '@/api/wrongQuestions'
import { getSubjects } from '@/api/subjects'
import { getUsers } from '@/api/users'

// 响应式数据
const loading = ref(false)
const detailDialogVisible = ref(false)
const analysisDialogVisible = ref(false)
const wrongQuestions = ref([])
const subjects = ref([])
const students = ref([])
const selectedWrongQuestions = ref([])
const currentWrongQuestion = ref(null)

// 搜索表单
const searchForm = reactive({
  studentId: '',
  subjectId: '',
  reviewStatus: '',
  isMastered: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 分析数据
const analysisData = reactive({
  totalCount: 0,
  masteredCount: 0,
  unmasteredCount: 0,
  subjectDistribution: []
})

const subjectChartRef = ref()

// 方法
const loadWrongQuestions = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page - 1,
      size: pagination.size,
      ...searchForm
    }
    const response = await getWrongQuestions(params)
    wrongQuestions.value = response.data.content
    pagination.total = response.data.totalElements
  } catch (error) {
    ElMessage.error('加载错题列表失败')
  } finally {
    loading.value = false
  }
}

const loadSubjects = async () => {
  try {
    const response = await getSubjects()
    subjects.value = response.data
  } catch (error) {
    ElMessage.error('加载学科列表失败')
  }
}

const loadStudents = async () => {
  try {
    const response = await getUsers({ role: 'STUDENT' })
    students.value = response.data.content || response.data
  } catch (error) {
    ElMessage.error('加载学生列表失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadWrongQuestions()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    studentId: '',
    subjectId: '',
    reviewStatus: '',
    isMastered: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadWrongQuestions()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadWrongQuestions()
}

const handleSelectionChange = (selection) => {
  selectedWrongQuestions.value = selection
}

const viewWrongQuestion = (row) => {
  currentWrongQuestion.value = row
  detailDialogVisible.value = true
}

const markAsMastered = async (row) => {
  try {
    await ElMessageBox.confirm('确定要标记这道错题为已掌握吗？', '确认标记', {
      type: 'info'
    })
    await markAsMasteredApi(row.id)
    ElMessage.success('标记成功')
    loadWrongQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('标记失败')
    }
  }
}

const deleteWrongQuestion = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条错题记录吗？', '确认删除', {
      type: 'warning'
    })
    await deleteWrongQuestionApi(row.id)
    ElMessage.success('删除成功')
    loadWrongQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const batchMarkAsMastered = async () => {
  try {
    await ElMessageBox.confirm(`确定要标记选中的 ${selectedWrongQuestions.value.length} 条错题为已掌握吗？`, '确认批量标记', {
      type: 'info'
    })
    // 这里应该调用批量标记API
    ElMessage.success('批量标记成功')
    selectedWrongQuestions.value = []
    loadWrongQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量标记失败')
    }
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedWrongQuestions.value.length} 条错题记录吗？`, '确认批量删除', {
      type: 'warning'
    })
    const ids = selectedWrongQuestions.value.map(item => item.id)
    await batchDeleteWrongQuestions(ids)
    ElMessage.success('批量删除成功')
    selectedWrongQuestions.value = []
    loadWrongQuestions()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const showAnalysisDialog = async () => {
  try {
    // 加载分析数据
    const response = await getWrongQuestionAnalysis()
    Object.assign(analysisData, response.data)
    analysisDialogVisible.value = true
    // 这里可以初始化图表
  } catch (error) {
    ElMessage.error('加载分析数据失败')
  }
}

// 辅助方法
const getReviewStatusText = (status) => {
  const map = {
    'PENDING': '待复习',
    'REVIEWING': '复习中',
    'MASTERED': '已掌握'
  }
  return map[status] || status
}

const getReviewStatusColor = (status) => {
  const map = {
    'PENDING': 'danger',
    'REVIEWING': 'warning',
    'MASTERED': 'success'
  }
  return map[status] || ''
}

// 生命周期
onMounted(() => {
  loadWrongQuestions()
  loadSubjects()
  loadStudents()
})
</script>

<style scoped>
.wrong-questions-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.batch-actions {
  margin: 20px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.wrong-question-detail {
  padding: 20px 0;
}

.question-content {
  margin: 20px 0;
}

.question-content h4 {
  margin-bottom: 10px;
  color: #303133;
}

.answers {
  margin: 20px 0;
}

.answers h4 {
  margin-bottom: 10px;
  color: #303133;
}

.student-answer {
  color: #f56c6c;
  background-color: #fef0f0;
  padding: 10px;
  border-radius: 4px;
}

.correct-answer {
  color: #67c23a;
  background-color: #f0f9ff;
  padding: 10px;
  border-radius: 4px;
}

.explanation, .notes {
  margin: 20px 0;
}

.explanation h4, .notes h4 {
  margin-bottom: 10px;
  color: #303133;
}

.analysis-content {
  padding: 20px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.stat-label {
  color: #606266;
}

.stat-value {
  font-weight: bold;
}

.stat-value.success {
  color: #67c23a;
}

.stat-value.danger {
  color: #f56c6c;
}

.chart-container {
  height: 300px;
}

@media (max-width: 768px) {
  .wrong-questions-page {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
