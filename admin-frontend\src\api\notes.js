import request from './request'

// 获取笔记列表
export function getNotes(params) {
  return request({
    url: '/notes',
    method: 'get',
    params
  })
}

// 获取笔记详情
export function getNote(id) {
  return request({
    url: `/notes/${id}`,
    method: 'get'
  })
}

// 创建笔记
export function createNote(data) {
  return request({
    url: '/notes',
    method: 'post',
    data
  })
}

// 更新笔记
export function updateNote(id, data) {
  return request({
    url: `/notes/${id}`,
    method: 'put',
    data
  })
}

// 删除笔记
export function deleteNote(id) {
  return request({
    url: `/notes/${id}`,
    method: 'delete'
  })
}

// 批量删除笔记
export function batchDeleteNotes(ids) {
  return request({
    url: '/notes/batch',
    method: 'delete',
    data: { ids }
  })
}

// 根据学生获取笔记
export function getNotesByStudent(studentId, params) {
  return request({
    url: `/notes/student/${studentId}`,
    method: 'get',
    params
  })
}

// 根据学科获取笔记
export function getNotesBySubject(subjectId, params) {
  return request({
    url: `/notes/subject/${subjectId}`,
    method: 'get',
    params
  })
}

// 分享笔记
export function shareNote(id) {
  return request({
    url: `/notes/${id}/share`,
    method: 'put'
  })
}

// 取消分享笔记
export function unshareNote(id) {
  return request({
    url: `/notes/${id}/unshare`,
    method: 'put'
  })
}

// 获取笔记统计信息
export function getNoteStats() {
  return request({
    url: '/notes/stats',
    method: 'get'
  })
}
