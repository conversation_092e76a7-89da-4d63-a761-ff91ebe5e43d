package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.dto.SubjectDTO;
import com.lait.service.SubjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 学科管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/subjects")
@RequiredArgsConstructor
@Validated
public class SubjectController {

    private final SubjectService subjectService;

    /**
     * 创建学科
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<SubjectDTO> createSubject(@Valid @RequestBody SubjectDTO.CreateSubjectRequest request) {
        try {
            SubjectDTO subject = subjectService.createSubject(request);
            return ApiResponse.success("学科创建成功", subject);
        } catch (Exception e) {
            log.error("创建学科失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取学科详情
     */
    @GetMapping("/{id}")
    public ApiResponse<SubjectDTO> getSubjectById(@PathVariable Long id) {
        try {
            SubjectDTO subject = subjectService.getSubjectById(id);
            return ApiResponse.success(subject);
        } catch (Exception e) {
            log.error("获取学科详情失败", e);
            return ApiResponse.notFound(e.getMessage());
        }
    }

    /**
     * 更新学科信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<SubjectDTO> updateSubject(@PathVariable Long id, 
                                                @Valid @RequestBody SubjectDTO.UpdateSubjectRequest request) {
        try {
            SubjectDTO subject = subjectService.updateSubject(id, request);
            return ApiResponse.success("学科信息更新成功", subject);
        } catch (Exception e) {
            log.error("更新学科信息失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 删除学科
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> deleteSubject(@PathVariable Long id) {
        try {
            subjectService.deleteSubject(id);
            return ApiResponse.success("学科删除成功");
        } catch (Exception e) {
            log.error("删除学科失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 分页查询学科
     */
    @GetMapping
    public ApiResponse<Page<SubjectDTO>> getSubjects(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "sortOrder") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir,
            @RequestParam(required = false) String keyword) {
        try {
            Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
            Pageable pageable = PageRequest.of(page, size, sort);

            Page<SubjectDTO> subjects;
            if (keyword != null && !keyword.trim().isEmpty()) {
                subjects = subjectService.searchSubjects(keyword, pageable);
            } else {
                subjects = subjectService.getSubjects(pageable);
            }

            return ApiResponse.success(subjects);
        } catch (Exception e) {
            log.error("查询学科列表失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 根据年级获取学科
     */
    @GetMapping("/grade/{gradeLevel}")
    public ApiResponse<List<SubjectDTO>> getSubjectsByGrade(@PathVariable Integer gradeLevel) {
        try {
            List<SubjectDTO> subjects = subjectService.getSubjectsByGrade(gradeLevel);
            return ApiResponse.success(subjects);
        } catch (Exception e) {
            log.error("获取年级学科失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 获取所有启用的学科
     */
    @GetMapping("/active")
    public ApiResponse<List<SubjectDTO>> getAllActiveSubjects() {
        try {
            List<SubjectDTO> subjects = subjectService.getAllActiveSubjects();
            return ApiResponse.success(subjects);
        } catch (Exception e) {
            log.error("获取启用学科失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 启用学科
     */
    @PutMapping("/{id}/activate")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> activateSubject(@PathVariable Long id) {
        try {
            subjectService.activateSubject(id);
            return ApiResponse.success("学科启用成功");
        } catch (Exception e) {
            log.error("启用学科失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 禁用学科
     */
    @PutMapping("/{id}/deactivate")
    @PreAuthorize("hasRole('ADMIN')")
    public ApiResponse<Void> deactivateSubject(@PathVariable Long id) {
        try {
            subjectService.deactivateSubject(id);
            return ApiResponse.success("学科禁用成功");
        } catch (Exception e) {
            log.error("禁用学科失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }

    /**
     * 检查学科代码是否存在
     */
    @GetMapping("/check-code")
    public ApiResponse<Boolean> checkSubjectCode(@RequestParam String subjectCode) {
        boolean exists = subjectService.existsBySubjectCode(subjectCode);
        return ApiResponse.success(exists);
    }
}
