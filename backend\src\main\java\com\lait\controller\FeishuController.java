package com.lait.controller;

import com.lait.entity.FeishuDocument;
import com.lait.service.FeishuService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 飞书文档管理控制器
 */
@RestController
@RequestMapping("/api/admin/feishu")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
public class FeishuController {

    private final FeishuService feishuService;

    // ========== 文档管理 ==========

    /**
     * 创建飞书文档记录
     */
    @PostMapping("/documents")
    public ResponseEntity<FeishuDocument> createDocument(@Valid @RequestBody FeishuDocument document) {
        FeishuDocument createdDocument = feishuService.createDocument(document);
        return ResponseEntity.ok(createdDocument);
    }

    /**
     * 更新飞书文档记录
     */
    @PutMapping("/documents/{id}")
    public ResponseEntity<FeishuDocument> updateDocument(@PathVariable Long id,
                                                        @Valid @RequestBody FeishuDocument document) {
        FeishuDocument updatedDocument = feishuService.updateDocument(id, document);
        return ResponseEntity.ok(updatedDocument);
    }

    /**
     * 删除飞书文档记录
     */
    @DeleteMapping("/documents/{id}")
    public ResponseEntity<Void> deleteDocument(@PathVariable Long id) {
        feishuService.deleteDocument(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取文档详情
     */
    @GetMapping("/documents/{id}")
    public ResponseEntity<FeishuDocument> getDocument(@PathVariable Long id) {
        FeishuDocument document = feishuService.getDocumentById(id);
        return ResponseEntity.ok(document);
    }

    /**
     * 根据文档ID获取文档
     */
    @GetMapping("/documents/doc/{docId}")
    public ResponseEntity<FeishuDocument> getDocumentByDocId(@PathVariable String docId) {
        FeishuDocument document = feishuService.getDocumentByDocId(docId);
        return ResponseEntity.ok(document);
    }

    /**
     * 分页查询文档
     */
    @GetMapping("/documents")
    public ResponseEntity<Page<FeishuDocument>> getDocuments(
            @RequestParam(required = false) String title,
            @RequestParam(required = false) FeishuDocument.DocumentType docType,
            @RequestParam(required = false) FeishuDocument.DocumentStatus status,
            @RequestParam(required = false) Long creatorId,
            @RequestParam(required = false) Long subjectId,
            @RequestParam(required = false) String category,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<FeishuDocument> documents = feishuService.getDocuments(title, docType, status, 
                                                                   creatorId, subjectId, category, pageable);
        return ResponseEntity.ok(documents);
    }

    /**
     * 搜索文档
     */
    @GetMapping("/documents/search")
    public ResponseEntity<Page<FeishuDocument>> searchDocuments(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<FeishuDocument> documents = feishuService.searchDocuments(keyword, pageable);
        return ResponseEntity.ok(documents);
    }

    /**
     * 获取热门文档
     */
    @GetMapping("/documents/popular")
    public ResponseEntity<List<FeishuDocument>> getPopularDocuments(
            @RequestParam(defaultValue = "10") int limit) {
        List<FeishuDocument> documents = feishuService.getPopularDocuments(limit);
        return ResponseEntity.ok(documents);
    }

    /**
     * 获取最近更新的文档
     */
    @GetMapping("/documents/recent")
    public ResponseEntity<List<FeishuDocument>> getRecentlyUpdatedDocuments(
            @RequestParam(defaultValue = "10") int limit) {
        List<FeishuDocument> documents = feishuService.getRecentlyUpdatedDocuments(limit);
        return ResponseEntity.ok(documents);
    }

    // ========== 飞书API集成 ==========

    /**
     * 从飞书同步文档内容
     */
    @PostMapping("/documents/{id}/sync")
    public ResponseEntity<FeishuDocument> syncDocumentFromFeishu(@PathVariable Long id) {
        FeishuDocument document = feishuService.getDocumentById(id);
        FeishuDocument syncedDocument = feishuService.syncDocumentFromFeishu(document.getDocId());
        return ResponseEntity.ok(syncedDocument);
    }

    /**
     * 批量同步文档
     */
    @PostMapping("/documents/batch-sync")
    public ResponseEntity<Void> batchSyncDocuments(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> documentIds = (List<Long>) request.get("documentIds");
        feishuService.batchSyncDocuments(documentIds);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取飞书文档内容
     */
    @GetMapping("/documents/{id}/content")
    public ResponseEntity<Map<String, Object>> getFeishuDocumentContent(@PathVariable Long id) {
        FeishuDocument document = feishuService.getDocumentById(id);
        String content = feishuService.getFeishuDocumentContent(document.getDocId());
        return ResponseEntity.ok(Map.of("content", content));
    }

    /**
     * 更新飞书文档内容
     */
    @PutMapping("/documents/{id}/content")
    public ResponseEntity<Map<String, Object>> updateFeishuDocumentContent(@PathVariable Long id,
                                                                          @RequestBody Map<String, String> request) {
        FeishuDocument document = feishuService.getDocumentById(id);
        String content = request.get("content");
        boolean success = feishuService.updateFeishuDocumentContent(document.getDocId(), content);
        return ResponseEntity.ok(Map.of("success", success));
    }

    /**
     * 获取飞书文档信息
     */
    @GetMapping("/documents/{id}/info")
    public ResponseEntity<Map<String, Object>> getFeishuDocumentInfo(@PathVariable Long id) {
        FeishuDocument document = feishuService.getDocumentById(id);
        Map<String, Object> info = feishuService.getFeishuDocumentInfo(document.getDocId());
        return ResponseEntity.ok(info);
    }

    /**
     * 创建飞书文档
     */
    @PostMapping("/documents/create-feishu")
    public ResponseEntity<Map<String, Object>> createFeishuDocument(@RequestBody Map<String, Object> request) {
        String title = (String) request.get("title");
        String content = (String) request.get("content");
        FeishuDocument.DocumentType docType = FeishuDocument.DocumentType.valueOf((String) request.get("docType"));
        
        Map<String, Object> result = feishuService.createFeishuDocument(title, content, docType);
        return ResponseEntity.ok(result);
    }

    /**
     * 导入飞书文档
     */
    @PostMapping("/documents/import")
    public ResponseEntity<FeishuDocument> importFeishuDocument(@RequestBody Map<String, String> request) {
        String docUrl = request.get("docUrl");
        FeishuDocument document = feishuService.importFeishuDocument(docUrl);
        return ResponseEntity.ok(document);
    }

    /**
     * 批量导入飞书文档
     */
    @PostMapping("/documents/batch-import")
    public ResponseEntity<List<FeishuDocument>> batchImportFeishuDocuments(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<String> docUrls = (List<String>) request.get("docUrls");
        List<FeishuDocument> documents = feishuService.batchImportFeishuDocuments(docUrls);
        return ResponseEntity.ok(documents);
    }

    // ========== 文档操作 ==========

    /**
     * 增加文档查看次数
     */
    @PostMapping("/documents/{id}/view")
    public ResponseEntity<Void> incrementDocumentViewCount(@PathVariable Long id) {
        feishuService.incrementDocumentViewCount(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 自动同步文档
     */
    @PostMapping("/documents/auto-sync")
    public ResponseEntity<Void> autoSyncDocuments() {
        feishuService.autoSyncDocuments();
        return ResponseEntity.ok().build();
    }

    /**
     * 获取需要同步的文档
     */
    @GetMapping("/documents/need-sync")
    public ResponseEntity<List<FeishuDocument>> getDocumentsNeedingSync() {
        List<FeishuDocument> documents = feishuService.getDocumentsNeedingSync();
        return ResponseEntity.ok(documents);
    }

    // ========== 权限管理 ==========

    /**
     * 设置文档访问权限
     */
    @PutMapping("/documents/{id}/access-level")
    public ResponseEntity<Void> setDocumentAccess(@PathVariable Long id,
                                                 @RequestBody Map<String, String> request) {
        FeishuDocument.AccessLevel accessLevel = FeishuDocument.AccessLevel.valueOf(request.get("accessLevel"));
        feishuService.setDocumentAccess(id, accessLevel);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取文档权限
     */
    @GetMapping("/documents/{id}/permissions")
    public ResponseEntity<Map<String, Object>> getFeishuDocumentPermissions(@PathVariable Long id) {
        FeishuDocument document = feishuService.getDocumentById(id);
        Map<String, Object> permissions = feishuService.getFeishuDocumentPermissions(document.getDocId());
        return ResponseEntity.ok(permissions);
    }

    /**
     * 设置文档权限
     */
    @PutMapping("/documents/{id}/permissions")
    public ResponseEntity<Map<String, Object>> setFeishuDocumentPermissions(@PathVariable Long id,
                                                                           @RequestBody Map<String, Object> permissions) {
        FeishuDocument document = feishuService.getDocumentById(id);
        boolean success = feishuService.setFeishuDocumentPermissions(document.getDocId(), permissions);
        return ResponseEntity.ok(Map.of("success", success));
    }

    // ========== 统计分析 ==========

    /**
     * 获取文档统计信息
     */
    @GetMapping("/statistics/documents")
    public ResponseEntity<Map<String, Object>> getDocumentStatistics() {
        Map<String, Object> stats = feishuService.getDocumentStatistics();
        return ResponseEntity.ok(stats);
    }

    /**
     * 获取用户文档统计
     */
    @GetMapping("/statistics/users/{userId}")
    public ResponseEntity<Map<String, Object>> getUserDocumentStatistics(@PathVariable Long userId) {
        Map<String, Object> stats = feishuService.getUserDocumentStatistics(userId);
        return ResponseEntity.ok(stats);
    }

    /**
     * 获取学科文档统计
     */
    @GetMapping("/statistics/subjects")
    public ResponseEntity<Map<String, Object>> getSubjectDocumentStatistics() {
        Map<String, Object> stats = feishuService.getSubjectDocumentStatistics();
        return ResponseEntity.ok(stats);
    }
}
