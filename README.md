
# 智能学习系统 (LAIT - Learning Assistance AI Tools)

## 项目简介
本项目是一个针对小学生设计的智能学习系统，分为后台管理系统和前端Web应用。系统结合了Coze平台，能够创建和管理AI智能体，为小学生提供个性化和智能化的学习体验。

## 项目结构
```
LAIT/
├── backend/                    # 后端服务 (Spring Boot + Java 8)
│   ├── src/main/java/com/lait/
│   │   ├── controller/         # REST API控制器
│   │   ├── service/           # 业务逻辑层
│   │   ├── repository/        # 数据访问层
│   │   ├── entity/           # 实体类
│   │   ├── dto/              # 数据传输对象
│   │   ├── config/           # 配置类
│   │   └── security/         # 安全相关
│   ├── src/main/resources/
│   │   ├── application.yml   # 应用配置
│   │   └── schema.sql       # 数据库表结构
│   └── pom.xml              # Maven配置
├── admin-frontend/            # 管理界面 (Vue 3 + Element Plus)
│   ├── src/
│   │   ├── views/           # 页面组件
│   │   ├── components/      # 通用组件
│   │   ├── layout/         # 布局组件
│   │   ├── router/         # 路由配置
│   │   ├── stores/         # 状态管理
│   │   └── api/            # API调用
│   └── package.json        # npm配置
├── student-frontend/          # 学生界面 (Vue 3 + Vant 4)
│   ├── src/
│   │   ├── views/          # 页面组件
│   │   ├── components/     # 通用组件
│   │   ├── layout/        # 布局组件
│   │   ├── router/        # 路由配置
│   │   ├── stores/        # 状态管理
│   │   └── api/           # API调用
│   └── package.json       # npm配置
├── start.bat                 # Windows启动脚本
└── README.md                # 项目说明文档
```

## 技术栈

### 后端
- **Java 8**: 作为主要的编程语言，提供稳定可靠的性能
- **Spring Boot 2.7.18**: 用于构建后端服务，简化开发和部署流程
- **Spring Security**: 提供认证和授权功能
- **Spring Data JPA**: 数据访问层框架
- **HikariCP**: 高性能数据库连接池
- **MySQL 8.0**: 关系型数据库
- **JWT**: JSON Web Token用于用户认证
- **Maven**: 项目构建和依赖管理

### 管理界面
- **Vue 3**: 现代化的前端框架
- **Element Plus**: 基于Vue 3的企业级UI组件库
- **Vite**: 快速的前端构建工具
- **Pinia**: Vue 3的状态管理库
- **Vue Router 4**: 路由管理
- **Axios**: HTTP客户端

### 学生界面
- **Vue 3**: 现代化的前端框架
- **Vant 4**: 基于Vue 3的移动端UI组件库
- **Vite**: 快速的前端构建工具
- **Pinia**: Vue 3的状态管理库
- **Vue Router 4**: 路由管理
- **Axios**: HTTP客户端

## 系统功能

### 后端管理系统
- **用户管理**: 管理学生、教师和管理员的账户
- **成绩管理**: 存储和管理学生的成绩信息
- **学科管理**: 管理可用的学科和课程
- **题目管理**: 创建和管理测试和练习的题目
- **错题管理**: 跟踪学生做错的题目，以便复习
- **笔记管理**: 允许学生创建笔记，并与飞书集成进行共享和同步
- **AI智能体集成**: 集成Coze平台API，提供智能化学习体验

### 管理界面功能 ✅ 已完成
- ✅ **仪表盘**：系统概览和统计数据，实时监控
- ✅ **用户管理**：增删改查用户信息，批量操作，角色管理
- ✅ **学科管理**：管理学科信息和配置，层级结构
- ✅ **题目管理**：题目的创建、编辑和分类，支持多种题型，批量导入
- ✅ **成绩管理**：学生成绩录入和统计，成绩分析，导出功能
- ✅ **错题管理**：错题分析和管理，复习建议，统计报表
- ✅ **笔记管理**：学生笔记的查看和管理，分享审核，标签管理

### 学生界面功能 ✅ 已完成
- ✅ **首页**: 学习统计、快捷功能、最近学习记录，个性化推荐
- ✅ **课程学习**: 按学科分类的课程内容和学习进度，资源下载
- ✅ **智能练习**: 多模式练习（按学科、随机、错题复习、模拟考试），实时反馈
- ✅ **个人中心**: 个人信息管理和学习数据查看，设置配置
- ✅ **错题本**: 错题自动收集、分类管理、复习提醒，掌握状态跟踪
- ✅ **笔记管理**: 富文本笔记编辑、分类管理、标签系统、分享功能
- ✅ **成绩查询**: 查看各科成绩和学习报告，趋势分析，排名对比

### 平板浏览器兼容性 ✅ 已完成
- ✅ **设备支持**: iPad系列、Android平板、Windows平板等主流设备
- ✅ **浏览器兼容**: Safari 12.0+、Chrome 80+、Edge 80+、Firefox 75+
- ✅ **响应式设计**: 768px-1023px小平板适配，1024px+大平板优化
- ✅ **触摸优化**: 44px+触摸目标，流畅手势交互，触摸反馈
- ✅ **性能优化**: 懒加载、代码分割、图片优化、缓存策略
- ✅ **无障碍支持**: 屏幕阅读器、键盘导航、高对比度、减少动画

## 快速开始

### 环境要求
- **Java 8**: 项目完全兼容Java 8，推荐使用JDK 1.8.0_301或更高版本
- **Node.js 16**: 或更高版本
- **MySQL 8.0**: 或更高版本（也支持MySQL 5.7）
- **Maven 3.6**: 或更高版本

### Java 8 兼容性说明
本项目专门针对Java 8进行了优化，确保在Java 8环境中的完全兼容性：
- 使用Spring Boot 2.7.18（最后一个支持Java 8的版本）
- 所有依赖都经过Java 8兼容性验证
- 代码中避免使用Java 9+的新特性
- 详细兼容性说明请查看：`backend/JAVA8_COMPATIBILITY.md`

### Java 8 兼容性验证
运行以下脚本验证Java 8兼容性：
```bash
cd backend

# Windows
verify-java8.bat

# Linux/macOS
chmod +x verify-java8.sh
./verify-java8.sh
```

### 数据库配置
1. 创建MySQL数据库：
```sql
CREATE DATABASE lait_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改后端配置文件 `backend/src/main/resources/application.yml`：
```yaml
spring:
  datasource:
    url: ********************************************************************************************************************
    username: your_username
    password: your_password
```

### 启动方式

#### 方式一：使用启动脚本（推荐）
双击运行 `start.bat` 文件，会自动启动所有服务。

#### 方式二：手动启动

1. **启动后端服务**
```bash
cd backend
mvn spring-boot:run
```

2. **启动管理界面**
```bash
cd admin-frontend
npm install
npm run dev
```

3. **启动学生界面**
```bash
cd student-frontend
npm install
npm run dev
```

### 访问地址
- 后端API: http://localhost:8080
- 管理界面: http://localhost:3000
- 学生界面: http://localhost:3001

### 默认账号
- 管理员账号: `admin` / `123456`

## API文档
后端提供RESTful API，主要接口包括：

### 认证接口
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息

### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

### 学科管理
- `GET /api/subjects` - 获取学科列表
- `POST /api/subjects` - 创建学科
- `PUT /api/subjects/{id}` - 更新学科
- `DELETE /api/subjects/{id}` - 删除学科

## 开发指南

### 后端开发
1. 实体类放在 `entity` 包下
2. 数据访问层接口放在 `repository` 包下
3. 业务逻辑放在 `service` 包下
4. 控制器放在 `controller` 包下
5. 使用 `@PreAuthorize` 注解进行权限控制

### 前端开发
1. 页面组件放在 `views` 目录下
2. 通用组件放在 `components` 目录下
3. API调用统一放在 `api` 目录下
4. 使用 Pinia 进行状态管理
5. 遵循 Vue 3 Composition API 规范

## 部署说明

### 后端部署
```bash
cd backend
mvn clean package
java -jar target/learning-assistance-backend-1.0.0.jar
```

### 前端部署
```bash
# 管理界面
cd admin-frontend
npm run build

# 学生界面
cd student-frontend
npm run build
```

## 贡献指南
我们欢迎对项目的贡献！请遵循以下步骤：

1. Fork 本仓库
2. 创建功能分支: `git checkout -b feature/your-feature-name`
3. 提交更改: `git commit -m 'Add some feature'`
4. 推送到分支: `git push origin feature/your-feature-name`
5. 创建 Pull Request

## 许可证
本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式
如有问题或建议，请通过以下方式联系：
- 邮箱: <EMAIL>
- GitHub Issues: [提交问题](https://github.com/yourusername/lait/issues)

## 更新日志

### v1.0.0 (2024-01-15) ✅ 已完成
- ✅ **后端完整实现**: Java 8兼容的Spring Boot后端，完整的RESTful API
- ✅ **管理界面完成**: Vue 3 + Element Plus，包含所有管理功能模块
- ✅ **学生界面完成**: Vue 3 + Vant 4，完整的学习管理功能
- ✅ **平板浏览器优化**: 完美支持iPad、Android平板、Windows平板
- ✅ **核心功能模块**:
  - 用户管理、学科管理、题目管理
  - 成绩管理、错题管理、笔记管理
  - 智能练习、错题本、成绩查询
- ✅ **技术特性**:
  - JWT认证和权限控制
  - 响应式设计和触摸优化
  - 性能优化和无障碍支持
  - Java 8完全兼容
