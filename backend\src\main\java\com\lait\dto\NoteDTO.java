package com.lait.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 笔记数据传输对象
 */
@Data
public class NoteDTO {
    
    private Long id;
    private Long studentId;
    private String studentName;
    private Long subjectId;
    private String subjectName;
    private String title;
    private String content;
    private String tags;
    private Boolean isShared;
    private String feishuDocId;
    private String feishuDocUrl;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * 创建笔记请求
     */
    @Data
    public static class CreateNoteRequest {
        
        @NotNull(message = "学生ID不能为空")
        private Long studentId;
        
        private Long subjectId;
        
        @NotBlank(message = "笔记标题不能为空")
        @Size(max = 200, message = "笔记标题长度不能超过200个字符")
        private String title;
        
        @NotBlank(message = "笔记内容不能为空")
        private String content;
        
        @Size(max = 200, message = "标签长度不能超过200个字符")
        private String tags;
        
        private Boolean isShared = false;
    }

    /**
     * 更新笔记请求
     */
    @Data
    public static class UpdateNoteRequest {
        
        @Size(max = 200, message = "笔记标题长度不能超过200个字符")
        private String title;
        
        private String content;
        
        @Size(max = 200, message = "标签长度不能超过200个字符")
        private String tags;
        
        private Boolean isShared;
        private Long subjectId;
    }

    /**
     * 笔记查询请求
     */
    @Data
    public static class NoteQueryRequest {
        private Long studentId;
        private Long subjectId;
        private String keyword;
        private String tags;
        private Boolean isShared;
        private String startDate;
        private String endDate;
    }

    /**
     * 分享笔记到飞书请求
     */
    @Data
    public static class ShareToFeishuRequest {
        
        @NotNull(message = "笔记ID不能为空")
        private Long noteId;
        
        private String folderToken; // 飞书文件夹token
        private Boolean isPublic = false; // 是否公开
    }

    /**
     * 从飞书同步笔记请求
     */
    @Data
    public static class SyncFromFeishuRequest {
        
        @NotNull(message = "学生ID不能为空")
        private Long studentId;
        
        @NotBlank(message = "飞书文档ID不能为空")
        private String feishuDocId;
        
        private Long subjectId;
        private String tags;
    }

    /**
     * 笔记统计信息
     */
    @Data
    public static class NoteStatistics {
        private Long studentId;
        private String studentName;
        private Integer totalNotes;
        private Integer sharedNotes;
        private Integer subjectCount;
        private LocalDateTime lastNoteTime;
    }
}
