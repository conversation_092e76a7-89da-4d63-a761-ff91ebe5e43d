server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: lait-backend

  # 数据库配置
  datasource:
    url: ********************************************************************************************************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai

# JWT配置
jwt:
  secret: lait-secret-key-for-jwt-token-generation
  expiration: 86400000 # 24小时

# Coze AI配置
coze:
  api:
    url: https://api.coze.cn/v1/chat
    token: ${COZE_API_TOKEN:}
  bot:
    id: ${COZE_BOT_ID:}

# 日志配置
logging:
  level:
    com.lait: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
