{"name": "lait-student-frontend", "version": "1.0.0", "description": "智能学习系统学生界面", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@vant/touch-emulator": "^1.4.0", "axios": "^1.4.0", "lait-student-frontend": "file:", "pinia": "^2.1.6", "vant": "^4.6.2", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.45.0", "eslint-plugin-vue": "^9.15.1", "prettier": "^3.0.0", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.5", "vite-plugin-style-import": "^2.0.0"}}