package com.lait.service.impl;

import com.lait.dto.SubjectDTO;
import com.lait.entity.Subject;
import com.lait.repository.SubjectRepository;
import com.lait.service.SubjectService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 学科服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubjectServiceImpl implements SubjectService {

    private final SubjectRepository subjectRepository;

    @Override
    @Transactional
    public SubjectDTO createSubject(SubjectDTO.CreateSubjectRequest request) {
        log.info("创建学科: {}", request.getName());

        // 检查学科代码是否已存在
        if (request.getSubjectCode() != null && subjectRepository.existsBySubjectCode(request.getSubjectCode())) {
            throw new RuntimeException("学科代码已存在");
        }

        Subject subject = new Subject();
        BeanUtils.copyProperties(request, subject);
        subject.setStatus(Subject.SubjectStatus.ACTIVE);

        Subject savedSubject = subjectRepository.save(subject);
        return convertToDTO(savedSubject);
    }

    @Override
    public SubjectDTO getSubjectById(Long id) {
        Subject subject = subjectRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("学科不存在"));
        return convertToDTO(subject);
    }

    @Override
    @Transactional
    public SubjectDTO updateSubject(Long id, SubjectDTO.UpdateSubjectRequest request) {
        log.info("更新学科信息: {}", id);

        Subject subject = subjectRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("学科不存在"));

        BeanUtils.copyProperties(request, subject, "id", "subjectCode");
        Subject savedSubject = subjectRepository.save(subject);
        return convertToDTO(savedSubject);
    }

    @Override
    @Transactional
    public void deleteSubject(Long id) {
        log.info("删除学科: {}", id);

        Subject subject = subjectRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("学科不存在"));

        subject.setIsDeleted(true);
        subjectRepository.save(subject);
    }

    @Override
    public Page<SubjectDTO> getSubjects(Pageable pageable) {
        Page<Subject> subjects = subjectRepository.findAllActive(pageable);
        return subjects.map(this::convertToDTO);
    }

    @Override
    public Page<SubjectDTO> searchSubjects(String keyword, Pageable pageable) {
        Page<Subject> subjects = subjectRepository.searchSubjects(keyword, pageable);
        return subjects.map(this::convertToDTO);
    }

    @Override
    public List<SubjectDTO> getSubjectsByGrade(Integer gradeLevel) {
        List<Subject> subjects = subjectRepository.findByGradeLevelAndStatus(gradeLevel, Subject.SubjectStatus.ACTIVE);
        return subjects.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<SubjectDTO> getAllActiveSubjects() {
        List<Subject> subjects = subjectRepository.findAllActiveSubjects();
        return subjects.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public boolean existsBySubjectCode(String subjectCode) {
        return subjectRepository.existsBySubjectCode(subjectCode);
    }

    @Override
    @Transactional
    public void activateSubject(Long id) {
        Subject subject = subjectRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("学科不存在"));
        subject.setStatus(Subject.SubjectStatus.ACTIVE);
        subjectRepository.save(subject);
    }

    @Override
    @Transactional
    public void deactivateSubject(Long id) {
        Subject subject = subjectRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("学科不存在"));
        subject.setStatus(Subject.SubjectStatus.INACTIVE);
        subjectRepository.save(subject);
    }

    /**
     * 实体转DTO
     */
    private SubjectDTO convertToDTO(Subject subject) {
        SubjectDTO dto = new SubjectDTO();
        BeanUtils.copyProperties(subject, dto);
        return dto;
    }
}
