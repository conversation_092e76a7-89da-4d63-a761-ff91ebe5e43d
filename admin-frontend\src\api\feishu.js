import request from './request'

// ========== 文档管理 ==========

// 获取飞书文档列表
export function getFeishuDocuments(params) {
  return request({
    url: '/admin/feishu/documents',
    method: 'get',
    params
  })
}

// 获取飞书文档详情
export function getFeishuDocument(id) {
  return request({
    url: `/admin/feishu/documents/${id}`,
    method: 'get'
  })
}

// 根据文档ID获取文档
export function getFeishuDocumentByDocId(docId) {
  return request({
    url: `/admin/feishu/documents/doc/${docId}`,
    method: 'get'
  })
}

// 创建飞书文档记录
export function createFeishuDocument(data) {
  return request({
    url: '/admin/feishu/documents',
    method: 'post',
    data
  })
}

// 更新飞书文档记录
export function updateFeishuDocument(id, data) {
  return request({
    url: `/admin/feishu/documents/${id}`,
    method: 'put',
    data
  })
}

// 删除飞书文档记录
export function deleteFeishuDocument(id) {
  return request({
    url: `/admin/feishu/documents/${id}`,
    method: 'delete'
  })
}

// 搜索文档
export function searchFeishuDocuments(keyword, params) {
  return request({
    url: '/admin/feishu/documents/search',
    method: 'get',
    params: { keyword, ...params }
  })
}

// 获取热门文档
export function getPopularFeishuDocuments(limit = 10) {
  return request({
    url: '/admin/feishu/documents/popular',
    method: 'get',
    params: { limit }
  })
}

// 获取最近更新的文档
export function getRecentFeishuDocuments(limit = 10) {
  return request({
    url: '/admin/feishu/documents/recent',
    method: 'get',
    params: { limit }
  })
}

// ========== 飞书API集成 ==========

// 从飞书同步文档内容
export function syncFeishuDocument(id) {
  return request({
    url: `/admin/feishu/documents/${id}/sync`,
    method: 'post'
  })
}

// 批量同步文档
export function batchSyncFeishuDocuments(documentIds) {
  return request({
    url: '/admin/feishu/documents/batch-sync',
    method: 'post',
    data: { documentIds }
  })
}

// 获取飞书文档内容
export function getFeishuDocumentContent(id) {
  return request({
    url: `/admin/feishu/documents/${id}/content`,
    method: 'get'
  })
}

// 更新飞书文档内容
export function updateFeishuDocumentContent(id, content) {
  return request({
    url: `/admin/feishu/documents/${id}/content`,
    method: 'put',
    data: { content }
  })
}

// 获取飞书文档信息
export function getFeishuDocumentInfo(id) {
  return request({
    url: `/admin/feishu/documents/${id}/info`,
    method: 'get'
  })
}

// 创建飞书文档
export function createFeishuDoc(title, content, docType) {
  return request({
    url: '/admin/feishu/documents/create-feishu',
    method: 'post',
    data: { title, content, docType }
  })
}

// 导入飞书文档
export function importFeishuDocument(docUrl) {
  return request({
    url: '/admin/feishu/documents/import',
    method: 'post',
    data: { docUrl }
  })
}

// 批量导入飞书文档
export function batchImportFeishuDocuments(docUrls) {
  return request({
    url: '/admin/feishu/documents/batch-import',
    method: 'post',
    data: { docUrls }
  })
}

// ========== 文档操作 ==========

// 增加文档查看次数
export function incrementFeishuDocumentViewCount(id) {
  return request({
    url: `/admin/feishu/documents/${id}/view`,
    method: 'post'
  })
}

// 自动同步文档
export function autoSyncFeishuDocuments() {
  return request({
    url: '/admin/feishu/documents/auto-sync',
    method: 'post'
  })
}

// 获取需要同步的文档
export function getFeishuDocumentsNeedingSync() {
  return request({
    url: '/admin/feishu/documents/need-sync',
    method: 'get'
  })
}

// ========== 权限管理 ==========

// 设置文档访问权限
export function setFeishuDocumentAccess(id, accessLevel) {
  return request({
    url: `/admin/feishu/documents/${id}/access-level`,
    method: 'put',
    data: { accessLevel }
  })
}

// 获取文档权限
export function getFeishuDocumentPermissions(id) {
  return request({
    url: `/admin/feishu/documents/${id}/permissions`,
    method: 'get'
  })
}

// 设置文档权限
export function setFeishuDocumentPermissions(id, permissions) {
  return request({
    url: `/admin/feishu/documents/${id}/permissions`,
    method: 'put',
    data: permissions
  })
}

// ========== 统计分析 ==========

// 获取文档统计信息
export function getFeishuDocumentStatistics() {
  return request({
    url: '/admin/feishu/statistics/documents',
    method: 'get'
  })
}

// 获取用户文档统计
export function getUserFeishuDocumentStatistics(userId) {
  return request({
    url: `/admin/feishu/statistics/users/${userId}`,
    method: 'get'
  })
}

// 获取学科文档统计
export function getSubjectFeishuDocumentStatistics() {
  return request({
    url: '/admin/feishu/statistics/subjects',
    method: 'get'
  })
}
