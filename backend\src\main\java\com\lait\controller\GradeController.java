package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.dto.GradeDTO;
import com.lait.service.GradeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 成绩管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/grades")
@RequiredArgsConstructor
@Validated
public class GradeController {

    private final GradeService gradeService;

    /**
     * 创建成绩
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<GradeDTO>> createGrade(
            @Valid @RequestBody GradeDTO.CreateGradeRequest request) {
        log.info("创建成绩请求: 学生ID={}, 学科ID={}", request.getStudentId(), request.getSubjectId());
        GradeDTO gradeDTO = gradeService.createGrade(request);
        return ResponseEntity.ok(ApiResponse.success(gradeDTO));
    }

    /**
     * 根据ID获取成绩
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<GradeDTO>> getGradeById(@PathVariable Long id) {
        GradeDTO gradeDTO = gradeService.getGradeById(id);
        return ResponseEntity.ok(ApiResponse.success(gradeDTO));
    }

    /**
     * 更新成绩信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<GradeDTO>> updateGrade(
            @PathVariable Long id,
            @Valid @RequestBody GradeDTO.UpdateGradeRequest request) {
        log.info("更新成绩请求: {}", id);
        GradeDTO gradeDTO = gradeService.updateGrade(id, request);
        return ResponseEntity.ok(ApiResponse.success(gradeDTO));
    }

    /**
     * 删除成绩
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Void>> deleteGrade(@PathVariable Long id) {
        log.info("删除成绩请求: {}", id);
        gradeService.deleteGrade(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 分页查询成绩
     */
    @GetMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Page<GradeDTO>>> getGrades(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<GradeDTO> grades = gradeService.getGrades(pageable);
        return ResponseEntity.ok(ApiResponse.success(grades));
    }

    /**
     * 根据学生ID分页查询成绩
     */
    @GetMapping("/student/{studentId}")
    public ResponseEntity<ApiResponse<Page<GradeDTO>>> getGradesByStudentId(
            @PathVariable Long studentId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<GradeDTO> grades = gradeService.getGradesByStudentId(studentId, pageable);
        return ResponseEntity.ok(ApiResponse.success(grades));
    }

    /**
     * 根据学科ID分页查询成绩
     */
    @GetMapping("/subject/{subjectId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Page<GradeDTO>>> getGradesBySubjectId(
            @PathVariable Long subjectId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<GradeDTO> grades = gradeService.getGradesBySubjectId(subjectId, pageable);
        return ResponseEntity.ok(ApiResponse.success(grades));
    }

    /**
     * 搜索成绩
     */
    @PostMapping("/search")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Page<GradeDTO>>> searchGrades(
            @RequestBody GradeDTO.GradeQueryRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<GradeDTO> grades = gradeService.searchGrades(request, pageable);
        return ResponseEntity.ok(ApiResponse.success(grades));
    }

    /**
     * 获取学生某学科的所有成绩
     */
    @GetMapping("/student/{studentId}/subject/{subjectId}")
    public ResponseEntity<ApiResponse<List<GradeDTO>>> getStudentSubjectGrades(
            @PathVariable Long studentId,
            @PathVariable Long subjectId) {
        
        List<GradeDTO> grades = gradeService.getStudentSubjectGrades(studentId, subjectId);
        return ResponseEntity.ok(ApiResponse.success(grades));
    }

    /**
     * 获取学生成绩统计
     */
    @GetMapping("/statistics/student/{studentId}/subject/{subjectId}")
    public ResponseEntity<ApiResponse<GradeDTO.GradeStatistics>> getStudentGradeStatistics(
            @PathVariable Long studentId,
            @PathVariable Long subjectId) {
        
        GradeDTO.GradeStatistics statistics = gradeService.getStudentGradeStatistics(studentId, subjectId);
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 获取班级成绩统计
     */
    @GetMapping("/statistics/class/{gradeLevel}/subject/{subjectId}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<List<GradeDTO.GradeStatistics>>> getClassGradeStatistics(
            @PathVariable Integer gradeLevel,
            @PathVariable Long subjectId) {
        
        List<GradeDTO.GradeStatistics> statistics = gradeService.getClassGradeStatistics(gradeLevel, subjectId);
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 发布成绩
     */
    @PutMapping("/{id}/publish")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Void>> publishGrade(@PathVariable Long id) {
        log.info("发布成绩请求: {}", id);
        gradeService.publishGrade(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 批量发布成绩
     */
    @PutMapping("/batch-publish")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Void>> batchPublishGrades(
            @RequestBody Long[] gradeIds) {
        log.info("批量发布成绩请求，数量: {}", gradeIds.length);
        gradeService.batchPublishGrades(gradeIds);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 归档成绩
     */
    @PutMapping("/{id}/archive")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Void>> archiveGrade(@PathVariable Long id) {
        log.info("归档成绩请求: {}", id);
        gradeService.archiveGrade(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 获取学生最新成绩
     */
    @GetMapping("/latest/student/{studentId}")
    public ResponseEntity<ApiResponse<List<GradeDTO>>> getStudentLatestGrades(
            @PathVariable Long studentId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<GradeDTO> grades = gradeService.getStudentLatestGrades(studentId, limit);
        return ResponseEntity.ok(ApiResponse.success(grades));
    }

    /**
     * 计算学生平均分
     */
    @GetMapping("/average/student/{studentId}/subject/{subjectId}")
    public ResponseEntity<ApiResponse<Double>> calculateStudentAverageScore(
            @PathVariable Long studentId,
            @PathVariable Long subjectId) {
        
        Double averageScore = gradeService.calculateStudentAverageScore(studentId, subjectId);
        return ResponseEntity.ok(ApiResponse.success(averageScore));
    }

    /**
     * 获取成绩趋势数据
     */
    @GetMapping("/trend/student/{studentId}/subject/{subjectId}")
    public ResponseEntity<ApiResponse<List<GradeDTO>>> getGradeTrend(
            @PathVariable Long studentId,
            @PathVariable Long subjectId,
            @RequestParam(defaultValue = "6") int months) {
        
        List<GradeDTO> grades = gradeService.getGradeTrend(studentId, subjectId, months);
        return ResponseEntity.ok(ApiResponse.success(grades));
    }
}
