package com.lait.repository;

import com.lait.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问层
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    Optional<User> findByUsernameOrEmail(String username, String email);

    /**
     * 根据角色查找用户
     */
    List<User> findByRole(User.UserRole role);

    /**
     * 根据状态查找用户
     */
    List<User> findByStatus(User.UserStatus status);

    /**
     * 根据年级查找学生
     */
    List<User> findByRoleAndGradeLevel(User.UserRole role, Integer gradeLevel);

    /**
     * 根据班级查找学生
     */
    List<User> findByRoleAndClassName(User.UserRole role, String className);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 分页查询用户（排除已删除）
     */
    @Query("SELECT u FROM User u WHERE u.isDeleted = false")
    Page<User> findAllActive(Pageable pageable);

    /**
     * 根据关键词搜索用户
     */
    @Query("SELECT u FROM User u WHERE u.isDeleted = false AND " +
           "(u.username LIKE %:keyword% OR u.realName LIKE %:keyword% OR u.email LIKE %:keyword%)")
    Page<User> searchUsers(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据角色分页查询用户
     */
    @Query("SELECT u FROM User u WHERE u.isDeleted = false AND u.role = :role")
    Page<User> findByRoleAndNotDeleted(@Param("role") User.UserRole role, Pageable pageable);
}
