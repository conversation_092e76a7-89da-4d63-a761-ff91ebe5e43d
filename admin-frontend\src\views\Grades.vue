<template>
  <div class="grades-page">
    <div class="page-header">
      <h1>成绩管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        录入成绩
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="学生">
          <el-select v-model="searchForm.studentId" placeholder="选择学生" clearable filterable>
            <el-option
              v-for="student in students"
              :key="student.id"
              :label="student.realName"
              :value="student.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学科">
          <el-select v-model="searchForm.subjectId" placeholder="选择学科" clearable>
            <el-option
              v-for="subject in subjects"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考试类型">
          <el-input v-model="searchForm.examType" placeholder="输入考试类型" clearable />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="草稿" value="DRAFT" />
            <el-option label="已发布" value="PUBLISHED" />
            <el-option label="已归档" value="ARCHIVED" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
          <el-button type="success" @click="exportGrades">导出</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 成绩列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="grades"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="studentName" label="学生姓名" width="120" />
        <el-table-column prop="subjectName" label="学科" width="100" />
        <el-table-column prop="examName" label="考试名称" min-width="150" show-overflow-tooltip />
        <el-table-column prop="examType" label="考试类型" width="120" />
        <el-table-column prop="score" label="得分" width="80" />
        <el-table-column prop="totalScore" label="总分" width="80" />
        <el-table-column label="得分率" width="100">
          <template #default="{ row }">
            {{ row.totalScore > 0 ? Math.round((row.score / row.totalScore) * 100) : 0 }}%
          </template>
        </el-table-column>
        <el-table-column prop="classRank" label="班级排名" width="100" />
        <el-table-column prop="gradeRank" label="年级排名" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdTime" label="录入时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewGrade(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editGrade(row)">编辑</el-button>
            <el-button size="small" type="danger" @click="deleteGrade(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedGrades.length > 0">
        <el-button type="success" @click="batchPublish">
          批量发布 ({{ selectedGrades.length }})
        </el-button>
        <el-button type="danger" @click="batchDelete">
          批量删除 ({{ selectedGrades.length }})
        </el-button>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑成绩' : '录入成绩'"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="学生" prop="studentId">
          <el-select v-model="form.studentId" placeholder="选择学生" filterable>
            <el-option
              v-for="student in students"
              :key="student.id"
              :label="student.realName"
              :value="student.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学科" prop="subjectId">
          <el-select v-model="form.subjectId" placeholder="选择学科">
            <el-option
              v-for="subject in subjects"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考试名称" prop="examName">
          <el-input v-model="form.examName" placeholder="请输入考试名称" />
        </el-form-item>
        <el-form-item label="考试类型" prop="examType">
          <el-input v-model="form.examType" placeholder="请输入考试类型" />
        </el-form-item>
        <el-form-item label="得分" prop="score">
          <el-input-number
            v-model="form.score"
            :min="0"
            :max="form.totalScore || 100"
            :precision="2"
            placeholder="请输入得分"
          />
        </el-form-item>
        <el-form-item label="总分" prop="totalScore">
          <el-input-number
            v-model="form.totalScore"
            :min="0"
            :precision="2"
            placeholder="请输入总分"
          />
        </el-form-item>
        <el-form-item label="班级排名">
          <el-input-number
            v-model="form.classRank"
            :min="1"
            placeholder="请输入班级排名"
          />
        </el-form-item>
        <el-form-item label="年级排名">
          <el-input-number
            v-model="form.gradeRank"
            :min="1"
            placeholder="请输入年级排名"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.comments"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="选择状态">
            <el-option label="草稿" value="DRAFT" />
            <el-option label="已发布" value="PUBLISHED" />
            <el-option label="已归档" value="ARCHIVED" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '录入' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getGrades, createGrade, updateGrade, deleteGrade as deleteGradeApi, batchDeleteGrades, exportGrades as exportGradesApi } from '@/api/grades'
import { getSubjects } from '@/api/subjects'
import { getUsers } from '@/api/users'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const grades = ref([])
const subjects = ref([])
const students = ref([])
const selectedGrades = ref([])

// 搜索表单
const searchForm = reactive({
  studentId: '',
  subjectId: '',
  examType: '',
  status: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  studentId: '',
  subjectId: '',
  examName: '',
  examType: '',
  score: 0,
  totalScore: 100,
  classRank: null,
  gradeRank: null,
  comments: '',
  status: 'DRAFT'
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  studentId: [{ required: true, message: '请选择学生', trigger: 'change' }],
  subjectId: [{ required: true, message: '请选择学科', trigger: 'change' }],
  examName: [{ required: true, message: '请输入考试名称', trigger: 'blur' }],
  examType: [{ required: true, message: '请输入考试类型', trigger: 'blur' }],
  score: [{ required: true, message: '请输入得分', trigger: 'blur' }],
  totalScore: [{ required: true, message: '请输入总分', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 方法
const loadGrades = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page - 1,
      size: pagination.size,
      ...searchForm
    }
    const response = await getGrades(params)
    grades.value = response.data.content
    pagination.total = response.data.totalElements
  } catch (error) {
    ElMessage.error('加载成绩列表失败')
  } finally {
    loading.value = false
  }
}

const loadSubjects = async () => {
  try {
    const response = await getSubjects()
    subjects.value = response.data
  } catch (error) {
    ElMessage.error('加载学科列表失败')
  }
}

const loadStudents = async () => {
  try {
    const response = await getUsers({ role: 'STUDENT' })
    students.value = response.data.content || response.data
  } catch (error) {
    ElMessage.error('加载学生列表失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadGrades()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    studentId: '',
    subjectId: '',
    examType: '',
    status: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadGrades()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadGrades()
}

const handleSelectionChange = (selection) => {
  selectedGrades.value = selection
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const viewGrade = (row) => {
  // 实现查看成绩详情
  ElMessage.info('查看功能待实现')
}

const editGrade = (row) => {
  isEdit.value = true
  Object.assign(form, {
    ...row,
    studentId: row.studentId || row.student?.id,
    subjectId: row.subjectId || row.subject?.id
  })
  dialogVisible.value = true
}

const deleteGrade = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这条成绩记录吗？', '确认删除', {
      type: 'warning'
    })
    await deleteGradeApi(row.id)
    ElMessage.success('删除成功')
    loadGrades()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedGrades.value.length} 条成绩记录吗？`, '确认批量删除', {
      type: 'warning'
    })
    const ids = selectedGrades.value.map(item => item.id)
    await batchDeleteGrades(ids)
    ElMessage.success('批量删除成功')
    selectedGrades.value = []
    loadGrades()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const batchPublish = async () => {
  try {
    await ElMessageBox.confirm(`确定要发布选中的 ${selectedGrades.value.length} 条成绩记录吗？`, '确认批量发布', {
      type: 'info'
    })
    // 这里应该调用批量发布API
    ElMessage.success('批量发布成功')
    selectedGrades.value = []
    loadGrades()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量发布失败')
    }
  }
}

const exportGrades = async () => {
  try {
    const response = await exportGradesApi(searchForm)
    // 处理文件下载
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `成绩导出_${new Date().toISOString().slice(0, 10)}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    const data = { ...form }
    if (isEdit.value) {
      await updateGrade(form.id, data)
      ElMessage.success('更新成功')
    } else {
      await createGrade(data)
      ElMessage.success('录入成功')
    }

    dialogVisible.value = false
    loadGrades()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '录入失败')
    }
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    studentId: '',
    subjectId: '',
    examName: '',
    examType: '',
    score: 0,
    totalScore: 100,
    classRank: null,
    gradeRank: null,
    comments: '',
    status: 'DRAFT'
  })
  formRef.value?.resetFields()
}

// 辅助方法
const getStatusText = (status) => {
  const map = {
    'DRAFT': '草稿',
    'PUBLISHED': '已发布',
    'ARCHIVED': '已归档'
  }
  return map[status] || status
}

const getStatusColor = (status) => {
  const map = {
    'DRAFT': 'info',
    'PUBLISHED': 'success',
    'ARCHIVED': 'warning'
  }
  return map[status] || ''
}

// 生命周期
onMounted(() => {
  loadGrades()
  loadSubjects()
  loadStudents()
})
</script>

<style scoped>
.grades-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.batch-actions {
  margin: 20px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.el-form--inline .el-form-item {
  margin-right: 20px;
}

.el-table .el-table__cell {
  padding: 12px 0;
}

.el-dialog .el-form {
  padding: 0 20px;
}

.el-dialog .el-form-item {
  margin-bottom: 20px;
}

.el-tag {
  margin-right: 5px;
}

.el-button + .el-button {
  margin-left: 8px;
}

@media (max-width: 768px) {
  .grades-page {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
