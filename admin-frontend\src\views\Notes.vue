<template>
  <div class="notes-page">
    <div class="page-header">
      <h1>笔记管理</h1>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        创建笔记
      </el-button>
    </div>

    <!-- 搜索和筛选 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="学生">
          <el-select v-model="searchForm.studentId" placeholder="选择学生" clearable filterable>
            <el-option
              v-for="student in students"
              :key="student.id"
              :label="student.realName"
              :value="student.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学科">
          <el-select v-model="searchForm.subjectId" placeholder="选择学科" clearable>
            <el-option
              v-for="subject in subjects"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="笔记类型">
          <el-select v-model="searchForm.noteType" placeholder="选择类型" clearable>
            <el-option label="学习笔记" value="STUDY" />
            <el-option label="复习笔记" value="REVIEW" />
            <el-option label="总结笔记" value="SUMMARY" />
            <el-option label="错题笔记" value="ERROR" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="选择状态" clearable>
            <el-option label="草稿" value="DRAFT" />
            <el-option label="已发布" value="PUBLISHED" />
            <el-option label="已归档" value="ARCHIVED" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否分享">
          <el-select v-model="searchForm.isShared" placeholder="选择" clearable>
            <el-option label="已分享" :value="true" />
            <el-option label="未分享" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 笔记列表 -->
    <el-card class="table-card">
      <el-table
        v-loading="loading"
        :data="notes"
        style="width: 100%"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="标题" min-width="200" show-overflow-tooltip />
        <el-table-column prop="studentName" label="学生姓名" width="120" />
        <el-table-column prop="subjectName" label="学科" width="100" />
        <el-table-column prop="noteType" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getNoteTypeColor(row.noteType)">
              {{ getNoteTypeText(row.noteType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusColor(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isShared" label="是否分享" width="100">
          <template #default="{ row }">
            <el-tag :type="row.isShared ? 'success' : 'info'">
              {{ row.isShared ? '已分享' : '未分享' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="viewCount" label="查看次数" width="100" />
        <el-table-column prop="tags" label="标签" width="150" show-overflow-tooltip />
        <el-table-column prop="createdTime" label="创建时间" width="180" />
        <el-table-column label="操作" width="250" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="viewNote(row)">查看</el-button>
            <el-button size="small" type="primary" @click="editNote(row)">编辑</el-button>
            <el-button
              size="small"
              :type="row.isShared ? 'warning' : 'success'"
              @click="toggleShare(row)"
            >
              {{ row.isShared ? '取消分享' : '分享' }}
            </el-button>
            <el-button size="small" type="danger" @click="deleteNote(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 批量操作 -->
      <div class="batch-actions" v-if="selectedNotes.length > 0">
        <el-button type="success" @click="batchShare">
          批量分享 ({{ selectedNotes.length }})
        </el-button>
        <el-button type="warning" @click="batchUnshare">
          批量取消分享 ({{ selectedNotes.length }})
        </el-button>
        <el-button type="danger" @click="batchDelete">
          批量删除 ({{ selectedNotes.length }})
        </el-button>
      </div>

      <!-- 分页 -->
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 创建/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑笔记' : '创建笔记'"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="学生" prop="studentId">
          <el-select v-model="form.studentId" placeholder="选择学生" filterable>
            <el-option
              v-for="student in students"
              :key="student.id"
              :label="student.realName"
              :value="student.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学科" prop="subjectId">
          <el-select v-model="form.subjectId" placeholder="选择学科">
            <el-option
              v-for="subject in subjects"
              :key="subject.id"
              :label="subject.name"
              :value="subject.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入笔记标题" />
        </el-form-item>
        <el-form-item label="笔记类型" prop="noteType">
          <el-select v-model="form.noteType" placeholder="选择类型">
            <el-option label="学习笔记" value="STUDY" />
            <el-option label="复习笔记" value="REVIEW" />
            <el-option label="总结笔记" value="SUMMARY" />
            <el-option label="错题笔记" value="ERROR" />
          </el-select>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="form.content"
            type="textarea"
            :rows="8"
            placeholder="请输入笔记内容"
          />
        </el-form-item>
        <el-form-item label="标签">
          <el-input
            v-model="form.tags"
            placeholder="请输入标签，用逗号分隔"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="form.status" placeholder="选择状态">
            <el-option label="草稿" value="DRAFT" />
            <el-option label="已发布" value="PUBLISHED" />
            <el-option label="已归档" value="ARCHIVED" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否分享">
          <el-switch v-model="form.isShared" />
        </el-form-item>
        <el-form-item label="飞书文档ID">
          <el-input
            v-model="form.feishuDocId"
            placeholder="飞书文档ID（可选）"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 笔记详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="笔记详情"
      width="800px"
    >
      <div v-if="currentNote" class="note-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="标题">
            {{ currentNote.title }}
          </el-descriptions-item>
          <el-descriptions-item label="学生姓名">
            {{ currentNote.studentName }}
          </el-descriptions-item>
          <el-descriptions-item label="学科">
            {{ currentNote.subjectName }}
          </el-descriptions-item>
          <el-descriptions-item label="类型">
            <el-tag :type="getNoteTypeColor(currentNote.noteType)">
              {{ getNoteTypeText(currentNote.noteType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusColor(currentNote.status)">
              {{ getStatusText(currentNote.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="是否分享">
            <el-tag :type="currentNote.isShared ? 'success' : 'info'">
              {{ currentNote.isShared ? '已分享' : '未分享' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="查看次数">
            {{ currentNote.viewCount }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ currentNote.createdTime }}
          </el-descriptions-item>
          <el-descriptions-item label="标签" :span="2">
            {{ currentNote.tags }}
          </el-descriptions-item>
        </el-descriptions>

        <div class="note-content">
          <h4>笔记内容</h4>
          <div class="content-text">{{ currentNote.content }}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getNotes, createNote, updateNote, deleteNote as deleteNoteApi, batchDeleteNotes, shareNote, unshareNote } from '@/api/notes'
import { getSubjects } from '@/api/subjects'
import { getUsers } from '@/api/users'

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const dialogVisible = ref(false)
const detailDialogVisible = ref(false)
const isEdit = ref(false)
const notes = ref([])
const subjects = ref([])
const students = ref([])
const selectedNotes = ref([])
const currentNote = ref(null)

// 搜索表单
const searchForm = reactive({
  studentId: '',
  subjectId: '',
  noteType: '',
  status: '',
  isShared: ''
})

// 分页
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 表单数据
const form = reactive({
  id: null,
  studentId: '',
  subjectId: '',
  title: '',
  content: '',
  noteType: '',
  status: 'DRAFT',
  isShared: false,
  tags: '',
  feishuDocId: ''
})

// 表单引用
const formRef = ref()

// 表单验证规则
const rules = {
  studentId: [{ required: true, message: '请选择学生', trigger: 'change' }],
  title: [{ required: true, message: '请输入笔记标题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入笔记内容', trigger: 'blur' }],
  noteType: [{ required: true, message: '请选择笔记类型', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

// 方法
const loadNotes = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page - 1,
      size: pagination.size,
      ...searchForm
    }
    const response = await getNotes(params)
    notes.value = response.data.content
    pagination.total = response.data.totalElements
  } catch (error) {
    ElMessage.error('加载笔记列表失败')
  } finally {
    loading.value = false
  }
}

const loadSubjects = async () => {
  try {
    const response = await getSubjects()
    subjects.value = response.data
  } catch (error) {
    ElMessage.error('加载学科列表失败')
  }
}

const loadStudents = async () => {
  try {
    const response = await getUsers({ role: 'STUDENT' })
    students.value = response.data.content || response.data
  } catch (error) {
    ElMessage.error('加载学生列表失败')
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadNotes()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    studentId: '',
    subjectId: '',
    noteType: '',
    status: '',
    isShared: ''
  })
  handleSearch()
}

const handleSizeChange = (size) => {
  pagination.size = size
  loadNotes()
}

const handleCurrentChange = (page) => {
  pagination.page = page
  loadNotes()
}

const handleSelectionChange = (selection) => {
  selectedNotes.value = selection
}

const showCreateDialog = () => {
  isEdit.value = false
  dialogVisible.value = true
}

const viewNote = (row) => {
  currentNote.value = row
  detailDialogVisible.value = true
}

const editNote = (row) => {
  isEdit.value = true
  Object.assign(form, {
    ...row,
    studentId: row.studentId || row.student?.id,
    subjectId: row.subjectId || row.subject?.id
  })
  dialogVisible.value = true
}

const toggleShare = async (row) => {
  try {
    const action = row.isShared ? '取消分享' : '分享'
    await ElMessageBox.confirm(`确定要${action}这篇笔记吗？`, `确认${action}`, {
      type: 'info'
    })

    if (row.isShared) {
      await unshareNote(row.id)
    } else {
      await shareNote(row.id)
    }

    ElMessage.success(`${action}成功`)
    loadNotes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

const deleteNote = async (row) => {
  try {
    await ElMessageBox.confirm('确定要删除这篇笔记吗？', '确认删除', {
      type: 'warning'
    })
    await deleteNoteApi(row.id)
    ElMessage.success('删除成功')
    loadNotes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
    }
  }
}

const batchShare = async () => {
  try {
    await ElMessageBox.confirm(`确定要分享选中的 ${selectedNotes.value.length} 篇笔记吗？`, '确认批量分享', {
      type: 'info'
    })
    // 这里应该调用批量分享API
    ElMessage.success('批量分享成功')
    selectedNotes.value = []
    loadNotes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量分享失败')
    }
  }
}

const batchUnshare = async () => {
  try {
    await ElMessageBox.confirm(`确定要取消分享选中的 ${selectedNotes.value.length} 篇笔记吗？`, '确认批量取消分享', {
      type: 'warning'
    })
    // 这里应该调用批量取消分享API
    ElMessage.success('批量取消分享成功')
    selectedNotes.value = []
    loadNotes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量取消分享失败')
    }
  }
}

const batchDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedNotes.value.length} 篇笔记吗？`, '确认批量删除', {
      type: 'warning'
    })
    const ids = selectedNotes.value.map(item => item.id)
    await batchDeleteNotes(ids)
    ElMessage.success('批量删除成功')
    selectedNotes.value = []
    loadNotes()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
    }
  }
}

const submitForm = async () => {
  try {
    await formRef.value.validate()
    submitting.value = true

    const data = { ...form }
    if (isEdit.value) {
      await updateNote(form.id, data)
      ElMessage.success('更新成功')
    } else {
      await createNote(data)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
    loadNotes()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  Object.assign(form, {
    id: null,
    studentId: '',
    subjectId: '',
    title: '',
    content: '',
    noteType: '',
    status: 'DRAFT',
    isShared: false,
    tags: '',
    feishuDocId: ''
  })
  formRef.value?.resetFields()
}

// 辅助方法
const getNoteTypeText = (type) => {
  const map = {
    'STUDY': '学习笔记',
    'REVIEW': '复习笔记',
    'SUMMARY': '总结笔记',
    'ERROR': '错题笔记'
  }
  return map[type] || type
}

const getNoteTypeColor = (type) => {
  const map = {
    'STUDY': 'primary',
    'REVIEW': 'success',
    'SUMMARY': 'warning',
    'ERROR': 'danger'
  }
  return map[type] || ''
}

const getStatusText = (status) => {
  const map = {
    'DRAFT': '草稿',
    'PUBLISHED': '已发布',
    'ARCHIVED': '已归档'
  }
  return map[status] || status
}

const getStatusColor = (status) => {
  const map = {
    'DRAFT': 'info',
    'PUBLISHED': 'success',
    'ARCHIVED': 'warning'
  }
  return map[status] || ''
}

// 生命周期
onMounted(() => {
  loadNotes()
  loadSubjects()
  loadStudents()
})
</script>

<style scoped>
.notes-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  color: #303133;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.batch-actions {
  margin: 20px 0;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.el-pagination {
  margin-top: 20px;
  text-align: right;
}

.note-detail {
  padding: 20px 0;
}

.note-content {
  margin: 20px 0;
}

.note-content h4 {
  margin-bottom: 10px;
  color: #303133;
}

.content-text {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.el-form--inline .el-form-item {
  margin-right: 20px;
}

.el-table .el-table__cell {
  padding: 12px 0;
}

.el-dialog .el-form {
  padding: 0 20px;
}

.el-dialog .el-form-item {
  margin-bottom: 20px;
}

.el-tag {
  margin-right: 5px;
}

.el-button + .el-button {
  margin-left: 8px;
}

@media (max-width: 768px) {
  .notes-page {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .el-form--inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 10px;
  }
}
</style>
