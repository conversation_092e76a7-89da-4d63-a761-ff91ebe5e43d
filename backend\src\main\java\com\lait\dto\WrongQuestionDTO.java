package com.lait.dto;

import com.lait.entity.WrongQuestion;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 错题数据传输对象
 */
@Data
public class WrongQuestionDTO {
    
    private Long id;
    private Long studentId;
    private String studentName;
    private Long questionId;
    private String questionTitle;
    private String questionContent;
    private String correctAnswer;
    private String studentAnswer;
    private Integer wrongCount;
    private Boolean isMastered;
    private String notes;
    private WrongQuestion.ReviewStatus reviewStatus;
    private String subjectName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    /**
     * 创建错题请求
     */
    @Data
    public static class CreateWrongQuestionRequest {
        
        @NotNull(message = "学生ID不能为空")
        private Long studentId;
        
        @NotNull(message = "题目ID不能为空")
        private Long questionId;
        
        private String studentAnswer;
        
        @Size(max = 500, message = "笔记长度不能超过500个字符")
        private String notes;
        
        private WrongQuestion.ReviewStatus reviewStatus = WrongQuestion.ReviewStatus.PENDING;
    }

    /**
     * 更新错题请求
     */
    @Data
    public static class UpdateWrongQuestionRequest {
        
        private String studentAnswer;
        private Boolean isMastered;
        
        @Size(max = 500, message = "笔记长度不能超过500个字符")
        private String notes;
        
        private WrongQuestion.ReviewStatus reviewStatus;
    }

    /**
     * 错题查询请求
     */
    @Data
    public static class WrongQuestionQueryRequest {
        private Long studentId;
        private Long subjectId;
        private WrongQuestion.ReviewStatus reviewStatus;
        private Boolean isMastered;
        private String keyword;
        private String startDate;
        private String endDate;
    }

    /**
     * 标记掌握请求
     */
    @Data
    public static class MarkMasteredRequest {
        
        @NotNull(message = "错题ID不能为空")
        private Long wrongQuestionId;
        
        @NotNull(message = "掌握状态不能为空")
        private Boolean isMastered;
        
        @Size(max = 500, message = "笔记长度不能超过500个字符")
        private String notes;
    }

    /**
     * 批量标记掌握请求
     */
    @Data
    public static class BatchMarkMasteredRequest {
        
        @NotNull(message = "错题ID列表不能为空")
        private Long[] wrongQuestionIds;
        
        @NotNull(message = "掌握状态不能为空")
        private Boolean isMastered;
    }

    /**
     * 错题统计信息
     */
    @Data
    public static class WrongQuestionStatistics {
        private Long studentId;
        private String studentName;
        private Integer totalWrongQuestions;
        private Integer pendingReview;
        private Integer reviewing;
        private Integer mastered;
        private Double masteryRate;
        private String mostWrongSubject;
        private Integer recentWrongCount;
    }

    /**
     * 学科错题统计
     */
    @Data
    public static class SubjectWrongStatistics {
        private Long subjectId;
        private String subjectName;
        private Integer wrongCount;
        private Integer masteredCount;
        private Double masteryRate;
        private String mostWrongQuestionType;
    }
}
