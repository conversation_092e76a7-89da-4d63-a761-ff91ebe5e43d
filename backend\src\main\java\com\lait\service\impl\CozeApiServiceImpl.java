package com.lait.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lait.dto.QuestionDTO;
import com.lait.entity.Question;
import com.lait.service.CozeApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Coze AI API服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CozeApiServiceImpl implements CozeApiService {

    @Value("${coze.api.url:https://api.coze.cn/v1/chat}")
    private String cozeApiUrl;

    @Value("${coze.api.token:}")
    private String cozeApiToken;

    @Value("${coze.bot.id:}")
    private String cozeBotId;

    private final ObjectMapper objectMapper;

    @Override
    public List<QuestionDTO> generateQuestions(GenerateQuestionRequest request) {
        log.info("调用Coze API生成题目: 学科={}, 类型={}, 难度={}, 数量={}", 
                request.getSubjectName(), request.getQuestionType(), request.getDifficulty(), request.getCount());

        try {
            // 构建提示词
            String prompt = buildQuestionGenerationPrompt(request);
            
            // 调用Coze API
            String response = callCozeApi(prompt);
            
            // 解析响应并转换为题目DTO
            return parseQuestionResponse(response, request);
            
        } catch (Exception e) {
            log.error("调用Coze API生成题目失败", e);
            // 返回模拟数据作为降级处理
            return generateMockQuestions(request);
        }
    }

    @Override
    public AnalysisResult analyzeStudentPerformance(AnalysisRequest request) {
        log.info("调用Coze API分析学生表现: 学生ID={}, 学科ID={}", request.getStudentId(), request.getSubjectId());

        try {
            // 构建分析提示词
            String prompt = buildAnalysisPrompt(request);
            
            // 调用Coze API
            String response = callCozeApi(prompt);
            
            // 解析响应
            return parseAnalysisResponse(response);
            
        } catch (Exception e) {
            log.error("调用Coze API分析学生表现失败", e);
            // 返回模拟分析结果
            return generateMockAnalysis(request);
        }
    }

    @Override
    public String generateStudyAdvice(StudyAdviceRequest request) {
        log.info("调用Coze API生成学习建议: 学生ID={}, 学科ID={}", request.getStudentId(), request.getSubjectId());

        try {
            // 构建学习建议提示词
            String prompt = buildStudyAdvicePrompt(request);
            
            // 调用Coze API
            return callCozeApi(prompt);
            
        } catch (Exception e) {
            log.error("调用Coze API生成学习建议失败", e);
            return "系统暂时无法生成个性化学习建议，请稍后再试。建议您继续复习薄弱知识点，多做练习题。";
        }
    }

    @Override
    public String explainAnswer(ExplainAnswerRequest request) {
        log.info("调用Coze API解释答案: 题目ID={}", request.getQuestionId());

        try {
            // 构建答案解释提示词
            String prompt = buildExplanationPrompt(request);
            
            // 调用Coze API
            return callCozeApi(prompt);
            
        } catch (Exception e) {
            log.error("调用Coze API解释答案失败", e);
            return "系统暂时无法提供详细解释，请参考题目原有解析或咨询老师。";
        }
    }

    /**
     * 调用Coze API
     */
    private String callCozeApi(String prompt) throws IOException {
        if (cozeApiToken.isEmpty() || cozeBotId.isEmpty()) {
            log.warn("Coze API配置不完整，返回模拟响应");
            return "模拟AI响应：" + prompt;
        }

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(cozeApiUrl);
            
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("Authorization", "Bearer " + cozeApiToken);
            
            // 构建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("bot_id", cozeBotId);
            requestBody.put("user_id", "lait_system");
            requestBody.put("query", prompt);
            requestBody.put("stream", false);
            
            String jsonBody = objectMapper.writeValueAsString(requestBody);
            httpPost.setEntity(new StringEntity(jsonBody, StandardCharsets.UTF_8));
            
            // 发送请求
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                
                if (response.getStatusLine().getStatusCode() == 200) {
                    // 解析响应，提取AI回复内容
                    return extractAiResponse(responseBody);
                } else {
                    log.error("Coze API调用失败，状态码: {}, 响应: {}", 
                            response.getStatusLine().getStatusCode(), responseBody);
                    throw new IOException("API调用失败");
                }
            }
        }
    }

    /**
     * 提取AI响应内容
     */
    private String extractAiResponse(String responseBody) {
        try {
            Map<String, Object> responseMap = objectMapper.readValue(responseBody, Map.class);
            // 根据Coze API的响应格式提取内容
            // 这里需要根据实际的API响应格式进行调整
            return responseBody; // 简化处理
        } catch (Exception e) {
            log.warn("解析AI响应失败，返回原始响应", e);
            return responseBody;
        }
    }

    /**
     * 构建题目生成提示词
     */
    private String buildQuestionGenerationPrompt(GenerateQuestionRequest request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请为").append(request.getSubjectName()).append("学科生成");
        prompt.append(request.getCount()).append("道");
        prompt.append(request.getQuestionType().getDescription());
        prompt.append("，难度为").append(request.getDifficulty().getDescription());
        prompt.append("，适合").append(request.getGradeLevel()).append("年级学生。");
        
        if (request.getTopic() != null) {
            prompt.append("题目主题：").append(request.getTopic()).append("。");
        }
        
        if (request.getRequirements() != null) {
            prompt.append("特殊要求：").append(request.getRequirements()).append("。");
        }
        
        prompt.append("请按照JSON格式返回，包含题目内容、选项（如适用）、正确答案和解析。");
        
        return prompt.toString();
    }

    /**
     * 构建分析提示词
     */
    private String buildAnalysisPrompt(AnalysisRequest request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请分析学生的答题表现。学生答题情况如下：\n");
        
        for (QuestionAnswerPair pair : request.getQuestionAnswers()) {
            prompt.append("题目：").append(pair.getQuestionContent()).append("\n");
            prompt.append("正确答案：").append(pair.getCorrectAnswer()).append("\n");
            prompt.append("学生答案：").append(pair.getStudentAnswer()).append("\n");
            prompt.append("是否正确：").append(pair.isCorrect() ? "是" : "否").append("\n\n");
        }
        
        prompt.append("请从以下几个方面进行分析：\n");
        prompt.append("1. 整体表现评价\n");
        prompt.append("2. 优势领域\n");
        prompt.append("3. 薄弱环节\n");
        prompt.append("4. 改进建议\n");
        prompt.append("5. 准确率统计\n");
        
        return prompt.toString();
    }

    /**
     * 构建学习建议提示词
     */
    private String buildStudyAdvicePrompt(StudyAdviceRequest request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请为学生制定个性化学习建议。学生情况如下：\n");
        prompt.append("当前水平：").append(request.getCurrentLevel()).append("\n");
        prompt.append("目标水平：").append(request.getTargetLevel()).append("\n");
        prompt.append("每日学习时间：").append(request.getStudyTimePerDay()).append("分钟\n");
        
        if (request.getWeakTopics() != null && !request.getWeakTopics().isEmpty()) {
            prompt.append("薄弱知识点：").append(String.join("、", request.getWeakTopics())).append("\n");
        }
        
        prompt.append("请提供具体的学习计划和方法建议。");
        
        return prompt.toString();
    }

    /**
     * 构建答案解释提示词
     */
    private String buildExplanationPrompt(ExplainAnswerRequest request) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请详细解释以下题目的答案：\n");
        prompt.append("题目：").append(request.getQuestionContent()).append("\n");
        prompt.append("正确答案：").append(request.getCorrectAnswer()).append("\n");
        
        if (request.getStudentAnswer() != null) {
            prompt.append("学生答案：").append(request.getStudentAnswer()).append("\n");
        }
        
        if (request.getExplanation() != null) {
            prompt.append("原有解析：").append(request.getExplanation()).append("\n");
        }
        
        prompt.append("请提供更详细的解题思路和知识点说明。");
        
        return prompt.toString();
    }

    /**
     * 解析题目响应
     */
    private List<QuestionDTO> parseQuestionResponse(String response, GenerateQuestionRequest request) {
        // 这里应该解析AI返回的JSON格式题目
        // 简化处理，返回模拟数据
        return generateMockQuestions(request);
    }

    /**
     * 解析分析响应
     */
    private AnalysisResult parseAnalysisResponse(String response) {
        // 这里应该解析AI返回的分析结果
        // 简化处理，返回基本结构
        AnalysisResult result = new AnalysisResult();
        result.setOverallPerformance(response);
        result.setStrengths(Arrays.asList("基础知识掌握较好"));
        result.setWeaknesses(Arrays.asList("需要加强练习"));
        result.setRecommendations(Arrays.asList("多做相关练习题"));
        result.setAccuracyRate(75.0);
        result.setDifficultyAssessment("中等");
        return result;
    }

    /**
     * 生成模拟题目（降级处理）
     */
    private List<QuestionDTO> generateMockQuestions(GenerateQuestionRequest request) {
        List<QuestionDTO> questions = new ArrayList<>();
        
        for (int i = 1; i <= request.getCount(); i++) {
            QuestionDTO question = new QuestionDTO();
            question.setSubjectId(request.getSubjectId());
            question.setSubjectName(request.getSubjectName());
            question.setContent("模拟" + request.getQuestionType().getDescription() + i);
            question.setType(request.getQuestionType());
            question.setDifficulty(request.getDifficulty());
            question.setGradeLevel(request.getGradeLevel());
            question.setCorrectAnswer("A");
            question.setExplanation("这是一道模拟题目的解析");
            
            if (request.getQuestionType() == Question.QuestionType.SINGLE_CHOICE ||
                request.getQuestionType() == Question.QuestionType.MULTIPLE_CHOICE) {
                question.setOptions("{\"A\":\"选项A\",\"B\":\"选项B\",\"C\":\"选项C\",\"D\":\"选项D\"}");
            }
            
            questions.add(question);
        }
        
        return questions;
    }

    /**
     * 生成模拟分析结果（降级处理）
     */
    private AnalysisResult generateMockAnalysis(AnalysisRequest request) {
        AnalysisResult result = new AnalysisResult();
        result.setOverallPerformance("整体表现良好，有进步空间");
        result.setStrengths(Arrays.asList("基础概念理解较好", "计算能力不错"));
        result.setWeaknesses(Arrays.asList("应用题解题思路需要加强", "细心程度有待提高"));
        result.setRecommendations(Arrays.asList("多练习应用题", "做题时要仔细审题", "加强基础知识复习"));
        
        // 计算准确率
        long correctCount = request.getQuestionAnswers().stream()
                .mapToLong(pair -> pair.isCorrect() ? 1 : 0)
                .sum();
        double accuracyRate = (double) correctCount / request.getQuestionAnswers().size() * 100;
        result.setAccuracyRate(accuracyRate);
        
        if (accuracyRate >= 80) {
            result.setDifficultyAssessment("优秀");
        } else if (accuracyRate >= 60) {
            result.setDifficultyAssessment("良好");
        } else {
            result.setDifficultyAssessment("需要加强");
        }
        
        return result;
    }
}
