import request from './request'

// 获取我的错题列表
export function getMyWrongQuestions(params) {
  return request({
    url: '/wrong-questions/my',
    method: 'get',
    params
  })
}

// 获取错题详情
export function getWrongQuestion(id) {
  return request({
    url: `/wrong-questions/${id}`,
    method: 'get'
  })
}

// 创建错题记录
export function createWrongQuestion(data) {
  return request({
    url: '/wrong-questions',
    method: 'post',
    data
  })
}

// 更新错题记录
export function updateWrongQuestion(id, data) {
  return request({
    url: `/wrong-questions/${id}`,
    method: 'put',
    data
  })
}

// 删除错题记录
export function deleteWrongQuestion(id) {
  return request({
    url: `/wrong-questions/${id}`,
    method: 'delete'
  })
}

// 标记错题为已掌握
export function markAsMastered(id) {
  return request({
    url: `/wrong-questions/${id}/master`,
    method: 'put'
  })
}

// 获取我的错题统计信息
export function getMyWrongQuestionStats() {
  return request({
    url: '/wrong-questions/my/stats',
    method: 'get'
  })
}

// 获取错题复习建议
export function getReviewSuggestions() {
  return request({
    url: '/wrong-questions/my/suggestions',
    method: 'get'
  })
}

// 根据学科获取错题
export function getWrongQuestionsBySubject(subjectId, params) {
  return request({
    url: `/wrong-questions/my/subject/${subjectId}`,
    method: 'get',
    params
  })
}
