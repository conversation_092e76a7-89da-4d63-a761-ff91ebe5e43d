package com.lait.service;

import com.lait.entity.CozeToken;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * Coze Token服务接口
 */
public interface CozeTokenService {

    /**
     * 创建Token
     */
    CozeToken createToken(CozeToken token);

    /**
     * 更新Token
     */
    CozeToken updateToken(Long id, CozeToken token);

    /**
     * 删除Token
     */
    void deleteToken(Long id);

    /**
     * 根据ID获取Token
     */
    CozeToken getTokenById(Long id);

    /**
     * 根据名称获取Token
     */
    CozeToken getTokenByName(String name);

    /**
     * 获取默认Token
     */
    CozeToken getDefaultToken();

    /**
     * 设置默认Token
     */
    void setDefaultToken(Long id);

    /**
     * 分页查询Token
     */
    Page<CozeToken> getTokens(String name, CozeToken.TokenType tokenType, 
                             CozeToken.TokenStatus status, Pageable pageable);

    /**
     * 获取所有激活的Token
     */
    List<CozeToken> getActiveTokens();

    /**
     * 验证Token有效性
     */
    boolean validateToken(String token);

    /**
     * 刷新Token状态
     */
    void refreshTokenStatus(Long id);

    /**
     * 更新Token使用信息
     */
    void updateTokenUsage(Long id);

    /**
     * 获取Token统计信息
     */
    Map<String, Object> getTokenStatistics();

    /**
     * 检查即将过期的Token
     */
    List<CozeToken> getExpiringTokens(int days);

    /**
     * 批量更新Token状态
     */
    void batchUpdateTokenStatus(List<Long> ids, CozeToken.TokenStatus status);

    /**
     * 测试Token连接
     */
    Map<String, Object> testTokenConnection(Long id);

    /**
     * 导出Token配置
     */
    String exportTokenConfig(List<Long> ids);

    /**
     * 导入Token配置
     */
    List<CozeToken> importTokenConfig(String config);
}
