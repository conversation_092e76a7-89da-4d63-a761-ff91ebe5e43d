-- 初始化数据脚本

-- 插入默认管理员用户
INSERT IGNORE INTO users (id, username, password, real_name, email, phone, role, grade_level, is_active, is_deleted, created_time, updated_time)
VALUES (1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdIx6QHkRn.4h8VUr6HjjGNOqF6', '系统管理员', '<EMAIL>', '13800138000', 'ADMIN', NULL, true, false, NOW(), NOW());

-- 插入默认教师用户
INSERT IGNORE INTO users (id, username, password, real_name, email, phone, role, grade_level, is_active, is_deleted, created_time, updated_time)
VALUES (2, 'teacher', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdIx6QHkRn.4h8VUr6HjjGNOqF6', '张老师', '<EMAIL>', '13800138001', 'TEACHER', NULL, true, false, NOW(), NOW());

-- 插入默认学生用户
INSERT IGNORE INTO users (id, username, password, real_name, email, phone, role, grade_level, is_active, is_deleted, created_time, updated_time)
VALUES (3, 'student', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8imdIx6QHkRn.4h8VUr6HjjGNOqF6', '李同学', '<EMAIL>', '13800138002', 'STUDENT', 9, true, false, NOW(), NOW());

-- 插入学科数据
INSERT IGNORE INTO subjects (id, name, description, grade_level, is_active, is_deleted, created_time, updated_time)
VALUES 
(1, '数学', '初中数学课程', 9, true, false, NOW(), NOW()),
(2, '语文', '初中语文课程', 9, true, false, NOW(), NOW()),
(3, '英语', '初中英语课程', 9, true, false, NOW(), NOW()),
(4, '物理', '初中物理课程', 9, true, false, NOW(), NOW()),
(5, '化学', '初中化学课程', 9, true, false, NOW(), NOW()),
(6, '生物', '初中生物课程', 9, true, false, NOW(), NOW()),
(7, '历史', '初中历史课程', 9, true, false, NOW(), NOW()),
(8, '地理', '初中地理课程', 9, true, false, NOW(), NOW());

-- 插入示例题目
INSERT IGNORE INTO questions (id, subject_id, content, question_type, difficulty, grade_level, options, correct_answer, explanation, tags, usage_count, correct_count, is_deleted, created_time, updated_time)
VALUES 
(1, 1, '计算：2 + 3 × 4 = ?', 'SINGLE_CHOICE', 'EASY', 9, '{"A":"10","B":"14","C":"20","D":"24"}', 'B', '根据运算顺序，先算乘法再算加法：2 + 3 × 4 = 2 + 12 = 14', '四则运算,运算顺序', 0, 0, false, NOW(), NOW()),
(2, 1, '解方程：2x + 5 = 13', 'FILL_BLANK', 'MEDIUM', 9, NULL, 'x = 4', '移项得：2x = 13 - 5 = 8，所以 x = 4', '一元一次方程', 0, 0, false, NOW(), NOW()),
(3, 2, '下列词语中，哪个是形容词？', 'SINGLE_CHOICE', 'EASY', 9, '{"A":"跑步","B":"美丽","C":"学习","D":"桌子"}', 'B', '美丽是形容词，用来形容事物的性质或状态', '词性,形容词', 0, 0, false, NOW(), NOW()),
(4, 3, 'What is the past tense of "go"?', 'SINGLE_CHOICE', 'EASY', 9, '{"A":"goed","B":"went","C":"gone","D":"going"}', 'B', 'The past tense of "go" is "went"', '动词时态,过去式', 0, 0, false, NOW(), NOW());

-- 插入示例笔记
INSERT IGNORE INTO notes (id, student_id, subject_id, title, content, note_type, status, tags, is_shared, view_count, feishu_doc_id, is_deleted, created_time, updated_time)
VALUES 
(1, 3, 1, '数学学习笔记', '今天学习了一元一次方程的解法，主要步骤包括：\n1. 移项\n2. 合并同类项\n3. 系数化为1', 'STUDY', 'PUBLISHED', '数学,方程', false, 0, NULL, false, NOW(), NOW()),
(2, 3, 2, '语文阅读心得', '读了《朝花夕拾》，感受到了鲁迅先生对童年生活的怀念和对社会现实的批判', 'READING', 'PUBLISHED', '语文,阅读', true, 5, NULL, false, NOW(), NOW());

-- 插入示例成绩
INSERT IGNORE INTO grades (id, student_id, subject_id, exam_name, score, full_score, exam_date, status, remarks, is_deleted, created_time, updated_time)
VALUES 
(1, 3, 1, '第一次月考', 85.5, 100.0, '2024-01-15', 'PUBLISHED', '表现良好，继续努力', false, NOW(), NOW()),
(2, 3, 2, '第一次月考', 92.0, 100.0, '2024-01-15', 'PUBLISHED', '优秀', false, NOW(), NOW()),
(3, 3, 3, '第一次月考', 78.0, 100.0, '2024-01-15', 'PUBLISHED', '需要加强口语练习', false, NOW(), NOW());

-- 插入示例错题
INSERT IGNORE INTO wrong_questions (id, student_id, question_id, student_answer, wrong_count, review_status, is_mastered, notes, is_deleted, created_time, updated_time)
VALUES 
(1, 3, 1, 'A', 1, 'PENDING', false, '忘记了运算顺序', false, NOW(), NOW()),
(2, 3, 2, 'x = 3', 2, 'REVIEWING', false, '移项时符号搞错了', false, NOW(), NOW());
