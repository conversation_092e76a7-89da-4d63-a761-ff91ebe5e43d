package com.lait.repository;

import com.lait.entity.Question;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 题目数据访问层
 */
@Repository
public interface QuestionRepository extends JpaRepository<Question, Long> {

    /**
     * 根据学科ID查找题目
     */
    List<Question> findBySubjectId(Long subjectId);

    /**
     * 根据题目类型查找题目
     */
    List<Question> findByQuestionType(Question.QuestionType questionType);

    /**
     * 根据难度等级查找题目
     */
    List<Question> findByDifficulty(Question.DifficultyLevel difficulty);

    /**
     * 根据年级查找题目
     */
    List<Question> findByGradeLevel(Integer gradeLevel);

    /**
     * 根据学科和年级查找题目
     */
    List<Question> findBySubjectIdAndGradeLevel(Long subjectId, Integer gradeLevel);

    /**
     * 根据学科、年级和难度查找题目
     */
    List<Question> findBySubjectIdAndGradeLevelAndDifficulty(
            Long subjectId, Integer gradeLevel, Question.DifficultyLevel difficulty);

    /**
     * 分页查询题目（排除已删除）
     */
    @Query("SELECT q FROM Question q WHERE q.isDeleted = false")
    Page<Question> findAllActive(Pageable pageable);

    /**
     * 根据学科分页查询题目
     */
    @Query("SELECT q FROM Question q WHERE q.isDeleted = false AND q.subjectId = :subjectId")
    Page<Question> findBySubjectIdAndNotDeleted(@Param("subjectId") Long subjectId, Pageable pageable);

    /**
     * 根据关键词搜索题目
     */
    @Query("SELECT q FROM Question q WHERE q.isDeleted = false AND " +
           "(q.content LIKE %:keyword% OR q.tags LIKE %:keyword%)")
    Page<Question> searchQuestions(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 随机获取题目
     */
    @Query(value = "SELECT * FROM questions WHERE is_deleted = false AND subject_id = :subjectId " +
                   "AND grade_level = :gradeLevel ORDER BY RAND() LIMIT :limit", nativeQuery = true)
    List<Question> findRandomQuestions(@Param("subjectId") Long subjectId, 
                                     @Param("gradeLevel") Integer gradeLevel, 
                                     @Param("limit") Integer limit);

    /**
     * 统计题目数量
     */
    @Query("SELECT COUNT(q) FROM Question q WHERE q.isDeleted = false AND q.subjectId = :subjectId")
    Long countBySubjectId(@Param("subjectId") Long subjectId);
}
