package com.lait.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import javax.validation.constraints.NotNull;

/**
 * 错题实体类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "wrong_questions")
public class WrongQuestion extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "学生ID不能为空")
    @Column(name = "student_id", nullable = false)
    private Long studentId;

    @NotNull(message = "题目ID不能为空")
    @Column(name = "question_id", nullable = false)
    private Long questionId;

    @Column(name = "student_answer")
    private String studentAnswer; // 学生答案

    @Column(name = "wrong_count")
    private Integer wrongCount = 1; // 错误次数

    @Column(name = "is_mastered")
    private Boolean isMastered = false; // 是否已掌握

    @Column(columnDefinition = "TEXT")
    private String notes; // 学生笔记

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private ReviewStatus reviewStatus;

    /**
     * 复习状态枚举
     */
    public enum ReviewStatus {
        PENDING("待复习"),
        REVIEWING("复习中"),
        MASTERED("已掌握");

        private final String description;

        ReviewStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
