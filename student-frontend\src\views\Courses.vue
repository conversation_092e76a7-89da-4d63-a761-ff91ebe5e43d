<template>
  <div class="courses">
    <!-- 顶部导航 -->
    <van-nav-bar title="课程学习" fixed placeholder />

    <!-- 学科选择 -->
    <van-tabs v-model:active="activeSubject" @change="onSubjectChange" sticky>
      <van-tab
        v-for="subject in subjects"
        :key="subject.id"
        :title="subject.name"
        :name="subject.id"
      >
        <!-- 课程列表 -->
        <div class="course-list">
          <van-cell-group
            v-for="chapter in chapters"
            :key="chapter.id"
            :title="chapter.name"
            class="chapter-group"
          >
            <van-cell
              v-for="lesson in chapter.lessons"
              :key="lesson.id"
              :title="lesson.title"
              :label="lesson.description"
              is-link
              @click="enterLesson(lesson)"
              class="lesson-item"
            >
              <template #icon>
                <div class="lesson-icon" :class="getLessonStatusClass(lesson.status)">
                  <van-icon :name="getLessonIcon(lesson.status)" />
                </div>
              </template>
              <template #right-icon>
                <div class="lesson-progress">
                  <van-circle
                    v-if="lesson.progress > 0"
                    :rate="lesson.progress"
                    :size="24"
                    :stroke-width="3"
                    color="#1989fa"
                    text=""
                  />
                  <span class="progress-text">{{ lesson.progress }}%</span>
                </div>
              </template>
            </van-cell>
          </van-cell-group>
        </div>
      </van-tab>
    </van-tabs>

    <!-- 学习统计 -->
    <van-cell-group title="学习统计" class="stats-section">
      <van-grid :column-num="3" :border="false">
        <van-grid-item
          v-for="stat in currentStats"
          :key="stat.key"
          :text="stat.label"
          class="stat-item"
        >
          <template #icon>
            <div class="stat-value">{{ stat.value }}</div>
          </template>
        </van-grid-item>
      </van-grid>
    </van-cell-group>

    <!-- 推荐课程 -->
    <van-cell-group title="推荐课程" class="recommendations">
      <van-swipe :autoplay="3000" indicator-color="white" class="recommendation-swipe">
        <van-swipe-item
          v-for="course in recommendedCourses"
          :key="course.id"
          @click="enterCourse(course)"
        >
          <div class="recommendation-card">
            <div class="card-content">
              <div class="course-title">{{ course.title }}</div>
              <div class="course-desc">{{ course.description }}</div>
              <van-tag type="primary" size="small">{{ course.difficulty }}</van-tag>
            </div>
          </div>
        </van-swipe-item>
      </van-swipe>
    </van-cell-group>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'

const router = useRouter()

const activeSubject = ref(1)

// 学科列表
const subjects = ref([
  { id: 1, name: '语文', code: 'CHINESE' },
  { id: 2, name: '数学', code: 'MATH' },
  { id: 3, name: '英语', code: 'ENGLISH' }
])

// 章节和课程数据
const chapters = ref([
  {
    id: 1,
    name: '第一章 基础知识',
    lessons: [
      {
        id: 1,
        title: '拼音学习',
        description: '学习声母、韵母和整体认读音节',
        status: 'completed',
        progress: 100
      },
      {
        id: 2,
        title: '汉字认读',
        description: '认识常用汉字，掌握基本笔画',
        status: 'learning',
        progress: 60
      },
      {
        id: 3,
        title: '词语理解',
        description: '理解词语含义，学会组词',
        status: 'locked',
        progress: 0
      }
    ]
  },
  {
    id: 2,
    name: '第二章 阅读理解',
    lessons: [
      {
        id: 4,
        title: '短文阅读',
        description: '阅读简单短文，理解文章内容',
        status: 'locked',
        progress: 0
      },
      {
        id: 5,
        title: '古诗背诵',
        description: '背诵经典古诗，理解诗意',
        status: 'locked',
        progress: 0
      }
    ]
  }
])

// 学习统计
const stats = reactive({
  1: [ // 语文
    { key: 'completed', label: '已完成', value: '5' },
    { key: 'learning', label: '学习中', value: '2' },
    { key: 'total', label: '总课程', value: '12' }
  ],
  2: [ // 数学
    { key: 'completed', label: '已完成', value: '8' },
    { key: 'learning', label: '学习中', value: '1' },
    { key: 'total', label: '总课程', value: '15' }
  ],
  3: [ // 英语
    { key: 'completed', label: '已完成', value: '3' },
    { key: 'learning', label: '学习中', value: '3' },
    { key: 'total', label: '总课程', value: '10' }
  ]
})

// 推荐课程
const recommendedCourses = ref([
  {
    id: 1,
    title: '趣味数学',
    description: '通过游戏学习数学，让数学变得有趣',
    difficulty: '简单'
  },
  {
    id: 2,
    title: '英语口语',
    description: '提高英语口语表达能力',
    difficulty: '中等'
  },
  {
    id: 3,
    title: '科学实验',
    description: '动手做实验，探索科学奥秘',
    difficulty: '简单'
  }
])

// 计算当前学科的统计数据
const currentStats = computed(() => {
  return stats[activeSubject.value] || []
})

// 方法
const onSubjectChange = (subjectId) => {
  loadChapters(subjectId)
}

const loadChapters = async (subjectId) => {
  try {
    // 这里应该根据学科ID加载对应的章节和课程
    // const response = await getChaptersAPI(subjectId)
    
    // 模拟不同学科的数据
    if (subjectId === 2) { // 数学
      chapters.value = [
        {
          id: 1,
          name: '第一章 数与运算',
          lessons: [
            {
              id: 1,
              title: '认识数字',
              description: '学习1-100的数字认读和书写',
              status: 'completed',
              progress: 100
            },
            {
              id: 2,
              title: '加法运算',
              description: '掌握两位数以内的加法运算',
              status: 'learning',
              progress: 75
            }
          ]
        }
      ]
    } else if (subjectId === 3) { // 英语
      chapters.value = [
        {
          id: 1,
          name: '第一章 字母学习',
          lessons: [
            {
              id: 1,
              title: '26个字母',
              description: '学习英文字母的读音和书写',
              status: 'completed',
              progress: 100
            },
            {
              id: 2,
              title: '简单单词',
              description: '学习常用的简单英语单词',
              status: 'learning',
              progress: 40
            }
          ]
        }
      ]
    }
  } catch (error) {
    showToast('加载课程失败')
  }
}

const enterLesson = (lesson) => {
  if (lesson.status === 'locked') {
    showToast('请先完成前面的课程')
    return
  }
  
  showToast(`进入课程: ${lesson.title}`)
  // 这里可以跳转到具体的课程学习页面
}

const enterCourse = (course) => {
  showToast(`推荐课程: ${course.title}`)
}

const getLessonStatusClass = (status) => {
  return {
    'completed': status === 'completed',
    'learning': status === 'learning',
    'locked': status === 'locked'
  }
}

const getLessonIcon = (status) => {
  const iconMap = {
    completed: 'success',
    learning: 'play-circle-o',
    locked: 'lock'
  }
  return iconMap[status] || 'play-circle-o'
}

onMounted(() => {
  loadChapters(activeSubject.value)
})
</script>

<style scoped>
.courses {
  background-color: #f7f8fa;
  min-height: 100vh;
}

.course-list {
  padding: 16px;
}

.chapter-group {
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.lesson-item {
  padding: 16px;
}

.lesson-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
}

.lesson-icon.completed {
  background-color: #07c160;
}

.lesson-icon.learning {
  background-color: #1989fa;
}

.lesson-icon.locked {
  background-color: #969799;
}

.lesson-progress {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.progress-text {
  font-size: 12px;
  color: #969799;
  margin-top: 4px;
}

.stats-section {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1989fa;
  margin-bottom: 4px;
}

.recommendations {
  margin: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.recommendation-swipe {
  height: 120px;
}

.recommendation-card {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin: 0 8px;
  border-radius: 8px;
}

.card-content {
  text-align: center;
  padding: 20px;
}

.course-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.course-desc {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 12px;
}
</style>
