package com.lait.service.impl;

import com.lait.dto.NoteDTO;
import com.lait.entity.Note;
import com.lait.entity.Subject;
import com.lait.entity.User;
import com.lait.repository.NoteRepository;
import com.lait.repository.SubjectRepository;
import com.lait.repository.UserRepository;
import com.lait.service.NoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 笔记服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class NoteServiceImpl implements NoteService {

    private final NoteRepository noteRepository;
    private final SubjectRepository subjectRepository;
    private final UserRepository userRepository;

    @Override
    @Transactional
    public NoteDTO createNote(NoteDTO.CreateNoteRequest request) {
        log.info("创建笔记: {}", request.getTitle());

        // 验证学生是否存在
        User student = userRepository.findById(request.getStudentId())
                .orElseThrow(() -> new RuntimeException("学生不存在"));

        // 验证学科是否存在（如果提供了学科ID）
        if (request.getSubjectId() != null) {
            subjectRepository.findById(request.getSubjectId())
                    .orElseThrow(() -> new RuntimeException("学科不存在"));
        }

        Note note = new Note();
        BeanUtils.copyProperties(request, note);
        note.setNoteType(Note.NoteType.STUDY); // 默认为学习笔记
        note.setStatus(Note.NoteStatus.DRAFT); // 默认为草稿状态

        Note savedNote = noteRepository.save(note);
        return convertToDTO(savedNote);
    }

    @Override
    public NoteDTO getNoteById(Long id) {
        Note note = noteRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));
        
        // 增加查看次数
        note.setViewCount(note.getViewCount() + 1);
        noteRepository.save(note);
        
        return convertToDTO(note);
    }

    @Override
    @Transactional
    public NoteDTO updateNote(Long id, NoteDTO.UpdateNoteRequest request) {
        log.info("更新笔记信息: {}", id);

        Note note = noteRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        // 验证学科是否存在（如果提供了学科ID）
        if (request.getSubjectId() != null) {
            subjectRepository.findById(request.getSubjectId())
                    .orElseThrow(() -> new RuntimeException("学科不存在"));
        }

        BeanUtils.copyProperties(request, note, "id", "studentId");
        Note savedNote = noteRepository.save(note);
        return convertToDTO(savedNote);
    }

    @Override
    @Transactional
    public void deleteNote(Long id) {
        log.info("删除笔记: {}", id);

        Note note = noteRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        note.setIsDeleted(true);
        noteRepository.save(note);
    }

    @Override
    public Page<NoteDTO> getNotes(Pageable pageable) {
        Page<Note> notes = noteRepository.findAllActive(pageable);
        return notes.map(this::convertToDTO);
    }

    @Override
    public Page<NoteDTO> getNotesByStudentId(Long studentId, Pageable pageable) {
        Page<Note> notes = noteRepository.findByStudentIdAndNotDeleted(studentId, pageable);
        return notes.map(this::convertToDTO);
    }

    @Override
    public Page<NoteDTO> getNotesBySubjectId(Long subjectId, Pageable pageable) {
        // 这里需要在Repository中添加相应的方法
        List<Note> notes = noteRepository.findBySubjectId(subjectId);
        return Page.empty(pageable);
    }

    @Override
    public Page<NoteDTO> searchNotes(NoteDTO.NoteQueryRequest request, Pageable pageable) {
        if (StringUtils.hasText(request.getKeyword())) {
            if (request.getStudentId() != null) {
                Page<Note> notes = noteRepository.searchNotesByStudent(
                        request.getStudentId(), request.getKeyword(), pageable);
                return notes.map(this::convertToDTO);
            } else {
                Page<Note> notes = noteRepository.searchNotes(request.getKeyword(), pageable);
                return notes.map(this::convertToDTO);
            }
        }
        return getNotes(pageable);
    }

    @Override
    public Page<NoteDTO> getSharedNotes(Pageable pageable) {
        List<Note> notes = noteRepository.findByIsSharedTrue();
        // 这里简化处理，实际应该在Repository中实现分页查询
        return Page.empty(pageable);
    }

    @Override
    public Page<NoteDTO> getNotesByTags(String tags, Pageable pageable) {
        // 这里需要在Repository中添加相应的方法
        return Page.empty(pageable);
    }

    @Override
    public String shareNoteToFeishu(NoteDTO.ShareToFeishuRequest request) {
        log.info("分享笔记到飞书: {}", request.getNoteId());

        Note note = noteRepository.findById(request.getNoteId())
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        // TODO: 实现飞书API集成
        // 这里应该调用飞书API创建文档
        String feishuDocId = "mock_feishu_doc_" + System.currentTimeMillis();
        
        note.setFeishuDocId(feishuDocId);
        note.setIsShared(request.getIsPublic());
        noteRepository.save(note);

        return "https://feishu.cn/docs/" + feishuDocId;
    }

    @Override
    @Transactional
    public NoteDTO syncNoteFromFeishu(NoteDTO.SyncFromFeishuRequest request) {
        log.info("从飞书同步笔记: {}", request.getFeishuDocId());

        // TODO: 实现飞书API集成
        // 这里应该调用飞书API获取文档内容
        
        Note note = new Note();
        note.setStudentId(request.getStudentId());
        note.setSubjectId(request.getSubjectId());
        note.setTitle("从飞书同步的笔记");
        note.setContent("同步的内容");
        note.setTags(request.getTags());
        note.setFeishuDocId(request.getFeishuDocId());
        note.setNoteType(Note.NoteType.STUDY);
        note.setStatus(Note.NoteStatus.PUBLISHED);

        Note savedNote = noteRepository.save(note);
        return convertToDTO(savedNote);
    }

    @Override
    public NoteDTO.NoteStatistics getStudentNoteStatistics(Long studentId) {
        User student = userRepository.findById(studentId)
                .orElseThrow(() -> new RuntimeException("学生不存在"));

        List<Note> notes = noteRepository.findByStudentId(studentId);
        
        NoteDTO.NoteStatistics statistics = new NoteDTO.NoteStatistics();
        statistics.setStudentId(studentId);
        statistics.setStudentName(student.getRealName());
        statistics.setTotalNotes(notes.size());
        statistics.setSharedNotes((int) notes.stream().filter(Note::getIsShared).count());
        statistics.setSubjectCount((int) notes.stream()
                .filter(note -> note.getSubjectId() != null)
                .map(Note::getSubjectId)
                .distinct()
                .count());
        
        notes.stream()
                .map(Note::getCreatedTime)
                .max(LocalDateTime::compareTo)
                .ifPresent(statistics::setLastNoteTime);

        return statistics;
    }

    @Override
    public List<NoteDTO> getRecentNotes(Long studentId, int limit) {
        Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createdTime"));
        Page<Note> notes = noteRepository.findByStudentIdAndNotDeleted(studentId, pageable);
        return notes.getContent().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Page<NoteDTO> searchNoteContent(String keyword, Pageable pageable) {
        Page<Note> notes = noteRepository.searchNotes(keyword, pageable);
        return notes.map(this::convertToDTO);
    }

    @Override
    @Transactional
    public void batchDeleteNotes(Long[] noteIds) {
        for (Long noteId : noteIds) {
            deleteNote(noteId);
        }
    }

    @Override
    @Transactional
    public void setNoteSharedStatus(Long noteId, Boolean isShared) {
        Note note = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));
        
        note.setIsShared(isShared);
        noteRepository.save(note);
    }

    @Override
    public List<String> getPopularTags(int limit) {
        List<Note> notes = noteRepository.findByIsSharedTrue();
        return notes.stream()
                .filter(note -> StringUtils.hasText(note.getTags()))
                .flatMap(note -> Arrays.stream(note.getTags().split(",")))
                .map(String::trim)
                .collect(Collectors.groupingBy(tag -> tag, Collectors.counting()))
                .entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e2.getValue(), e1.getValue()))
                .limit(limit)
                .map(entry -> entry.getKey())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public NoteDTO duplicateNote(Long noteId, Long targetStudentId) {
        Note originalNote = noteRepository.findById(noteId)
                .orElseThrow(() -> new RuntimeException("笔记不存在"));

        // 验证目标学生是否存在
        userRepository.findById(targetStudentId)
                .orElseThrow(() -> new RuntimeException("目标学生不存在"));

        Note newNote = new Note();
        BeanUtils.copyProperties(originalNote, newNote, "id", "studentId", "viewCount", "feishuDocId");
        newNote.setStudentId(targetStudentId);
        newNote.setTitle(originalNote.getTitle() + " (副本)");
        newNote.setIsShared(false);

        Note savedNote = noteRepository.save(newNote);
        return convertToDTO(savedNote);
    }

    /**
     * 转换为DTO
     */
    private NoteDTO convertToDTO(Note note) {
        NoteDTO dto = new NoteDTO();
        BeanUtils.copyProperties(note, dto);
        
        // 设置学生名称
        userRepository.findById(note.getStudentId())
                .ifPresent(student -> dto.setStudentName(student.getRealName()));
        
        // 设置学科名称
        if (note.getSubjectId() != null) {
            subjectRepository.findById(note.getSubjectId())
                    .ifPresent(subject -> dto.setSubjectName(subject.getName()));
        }
        
        // 设置飞书文档URL
        if (StringUtils.hasText(note.getFeishuDocId())) {
            dto.setFeishuDocUrl("https://feishu.cn/docs/" + note.getFeishuDocId());
        }
        
        return dto;
    }
}
