package com.lait.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 积分配置实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "points_config")
public class PointsConfig extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 配置项名称
     */
    @Column(nullable = false, unique = true, length = 100)
    private String configKey;

    /**
     * 配置项显示名称
     */
    @Column(nullable = false, length = 100)
    private String displayName;

    /**
     * 积分值
     */
    @Column(nullable = false)
    private Integer points;

    /**
     * 配置分类
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private ConfigCategory category;

    /**
     * 是否启用
     */
    @Column(nullable = false)
    private Boolean enabled = true;

    /**
     * 每日限制次数 (0表示无限制)
     */
    @Column(nullable = false)
    private Integer dailyLimit = 0;

    /**
     * 描述
     */
    @Column(length = 500)
    private String description;

    /**
     * 排序
     */
    @Column(nullable = false)
    private Integer sortOrder = 0;

    /**
     * 配置分类枚举
     */
    public enum ConfigCategory {
        LEARNING("学习行为"),
        PRACTICE("练习行为"),
        SOCIAL("社交行为"),
        ACHIEVEMENT("成就奖励"),
        PENALTY("扣分项目");

        private final String description;

        ConfigCategory(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
