package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.dto.QuestionDTO;
import com.lait.entity.Question;
import com.lait.service.QuestionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 题目管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/questions")
@RequiredArgsConstructor
@Validated
public class QuestionController {

    private final QuestionService questionService;

    /**
     * 创建题目
     */
    @PostMapping
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<QuestionDTO>> createQuestion(
            @Valid @RequestBody QuestionDTO.CreateQuestionRequest request) {
        log.info("创建题目请求: {}", request.getContent());
        QuestionDTO questionDTO = questionService.createQuestion(request);
        return ResponseEntity.ok(ApiResponse.success(questionDTO));
    }

    /**
     * 根据ID获取题目
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<QuestionDTO>> getQuestionById(@PathVariable Long id) {
        QuestionDTO questionDTO = questionService.getQuestionById(id);
        return ResponseEntity.ok(ApiResponse.success(questionDTO));
    }

    /**
     * 更新题目信息
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<QuestionDTO>> updateQuestion(
            @PathVariable Long id,
            @Valid @RequestBody QuestionDTO.UpdateQuestionRequest request) {
        log.info("更新题目请求: {}", id);
        QuestionDTO questionDTO = questionService.updateQuestion(id, request);
        return ResponseEntity.ok(ApiResponse.success(questionDTO));
    }

    /**
     * 删除题目
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Void>> deleteQuestion(@PathVariable Long id) {
        log.info("删除题目请求: {}", id);
        questionService.deleteQuestion(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 分页查询题目
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<QuestionDTO>>> getQuestions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<QuestionDTO> questions = questionService.getQuestions(pageable);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 根据学科ID分页查询题目
     */
    @GetMapping("/subject/{subjectId}")
    public ResponseEntity<ApiResponse<Page<QuestionDTO>>> getQuestionsBySubjectId(
            @PathVariable Long subjectId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<QuestionDTO> questions = questionService.getQuestionsBySubjectId(subjectId, pageable);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 搜索题目
     */
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<Page<QuestionDTO>>> searchQuestions(
            @RequestBody QuestionDTO.QuestionQueryRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<QuestionDTO> questions = questionService.searchQuestions(request, pageable);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 根据难度等级查询题目
     */
    @GetMapping("/difficulty/{difficulty}")
    public ResponseEntity<ApiResponse<Page<QuestionDTO>>> getQuestionsByDifficulty(
            @PathVariable Question.DifficultyLevel difficulty,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<QuestionDTO> questions = questionService.getQuestionsByDifficulty(difficulty, pageable);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 根据题目类型查询题目
     */
    @GetMapping("/type/{type}")
    public ResponseEntity<ApiResponse<Page<QuestionDTO>>> getQuestionsByType(
            @PathVariable Question.QuestionType type,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<QuestionDTO> questions = questionService.getQuestionsByType(type, pageable);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 根据年级查询题目
     */
    @GetMapping("/grade/{gradeLevel}")
    public ResponseEntity<ApiResponse<Page<QuestionDTO>>> getQuestionsByGradeLevel(
            @PathVariable Integer gradeLevel,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<QuestionDTO> questions = questionService.getQuestionsByGradeLevel(gradeLevel, pageable);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 随机获取题目
     */
    @GetMapping("/random")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> getRandomQuestions(
            @RequestParam Long subjectId,
            @RequestParam Question.DifficultyLevel difficulty,
            @RequestParam(defaultValue = "10") int count) {
        
        List<QuestionDTO> questions = questionService.getRandomQuestions(subjectId, difficulty, count);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 批量导入题目
     */
    @PostMapping("/batch-import")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> batchImportQuestions(
            @Valid @RequestBody QuestionDTO.BatchImportRequest request) {
        log.info("批量导入题目请求，数量: {}", request.getQuestions().size());
        List<QuestionDTO> questions = questionService.batchImportQuestions(request);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 获取题目统计信息
     */
    @GetMapping("/{id}/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<QuestionDTO.QuestionStatistics>> getQuestionStatistics(
            @PathVariable Long id) {
        QuestionDTO.QuestionStatistics statistics = questionService.getQuestionStatistics(id);
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 更新题目使用次数
     */
    @PostMapping("/{id}/increment-usage")
    public ResponseEntity<ApiResponse<Void>> incrementUsageCount(@PathVariable Long id) {
        questionService.incrementUsageCount(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 更新题目正确次数
     */
    @PostMapping("/{id}/increment-correct")
    public ResponseEntity<ApiResponse<Void>> incrementCorrectCount(@PathVariable Long id) {
        questionService.incrementCorrectCount(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 获取热门题目
     */
    @GetMapping("/popular")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> getPopularQuestions(
            @RequestParam Long subjectId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<QuestionDTO> questions = questionService.getPopularQuestions(subjectId, limit);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 获取错误率最高的题目
     */
    @GetMapping("/most-wrong")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> getMostWrongQuestions(
            @RequestParam Long subjectId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<QuestionDTO> questions = questionService.getMostWrongQuestions(subjectId, limit);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * 复制题目
     */
    @PostMapping("/{id}/duplicate")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<QuestionDTO>> duplicateQuestion(@PathVariable Long id) {
        log.info("复制题目请求: {}", id);
        QuestionDTO questionDTO = questionService.duplicateQuestion(id);
        return ResponseEntity.ok(ApiResponse.success(questionDTO));
    }

    /**
     * 批量删除题目
     */
    @DeleteMapping("/batch")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<Void>> batchDeleteQuestions(
            @RequestBody Long[] questionIds) {
        log.info("批量删除题目请求，数量: {}", questionIds.length);
        questionService.batchDeleteQuestions(questionIds);
        return ResponseEntity.ok(ApiResponse.success(null));
    }
}
