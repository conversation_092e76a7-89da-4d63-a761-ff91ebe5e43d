package com.lait.controller;

import com.lait.entity.CozeToken;
import com.lait.service.CozeTokenService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * Coze Token管理控制器
 */
@RestController
@RequestMapping("/api/admin/coze-tokens")
@RequiredArgsConstructor
@PreAuthorize("hasRole('ADMIN')")
public class CozeTokenController {

    private final CozeTokenService cozeTokenService;

    /**
     * 创建Token
     */
    @PostMapping
    public ResponseEntity<CozeToken> createToken(@Valid @RequestBody CozeToken token) {
        CozeToken createdToken = cozeTokenService.createToken(token);
        return ResponseEntity.ok(createdToken);
    }

    /**
     * 更新Token
     */
    @PutMapping("/{id}")
    public ResponseEntity<CozeToken> updateToken(@PathVariable Long id, 
                                               @Valid @RequestBody CozeToken token) {
        CozeToken updatedToken = cozeTokenService.updateToken(id, token);
        return ResponseEntity.ok(updatedToken);
    }

    /**
     * 删除Token
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteToken(@PathVariable Long id) {
        cozeTokenService.deleteToken(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 获取Token详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<CozeToken> getToken(@PathVariable Long id) {
        CozeToken token = cozeTokenService.getTokenById(id);
        return ResponseEntity.ok(token);
    }

    /**
     * 分页查询Token
     */
    @GetMapping
    public ResponseEntity<Page<CozeToken>> getTokens(
            @RequestParam(required = false) String name,
            @RequestParam(required = false) CozeToken.TokenType tokenType,
            @RequestParam(required = false) CozeToken.TokenStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<CozeToken> tokens = cozeTokenService.getTokens(name, tokenType, status, pageable);
        return ResponseEntity.ok(tokens);
    }

    /**
     * 获取所有激活的Token
     */
    @GetMapping("/active")
    public ResponseEntity<List<CozeToken>> getActiveTokens() {
        List<CozeToken> tokens = cozeTokenService.getActiveTokens();
        return ResponseEntity.ok(tokens);
    }

    /**
     * 获取默认Token
     */
    @GetMapping("/default")
    public ResponseEntity<CozeToken> getDefaultToken() {
        CozeToken token = cozeTokenService.getDefaultToken();
        return ResponseEntity.ok(token);
    }

    /**
     * 设置默认Token
     */
    @PutMapping("/{id}/set-default")
    public ResponseEntity<Void> setDefaultToken(@PathVariable Long id) {
        cozeTokenService.setDefaultToken(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 验证Token
     */
    @PostMapping("/validate")
    public ResponseEntity<Map<String, Object>> validateToken(@RequestBody Map<String, String> request) {
        String token = request.get("token");
        boolean isValid = cozeTokenService.validateToken(token);
        return ResponseEntity.ok(Map.of("valid", isValid));
    }

    /**
     * 刷新Token状态
     */
    @PutMapping("/{id}/refresh-status")
    public ResponseEntity<Void> refreshTokenStatus(@PathVariable Long id) {
        cozeTokenService.refreshTokenStatus(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 测试Token连接
     */
    @PostMapping("/{id}/test")
    public ResponseEntity<Map<String, Object>> testTokenConnection(@PathVariable Long id) {
        Map<String, Object> result = cozeTokenService.testTokenConnection(id);
        return ResponseEntity.ok(result);
    }

    /**
     * 获取Token统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getTokenStatistics() {
        Map<String, Object> stats = cozeTokenService.getTokenStatistics();
        return ResponseEntity.ok(stats);
    }

    /**
     * 获取即将过期的Token
     */
    @GetMapping("/expiring")
    public ResponseEntity<List<CozeToken>> getExpiringTokens(
            @RequestParam(defaultValue = "7") int days) {
        List<CozeToken> tokens = cozeTokenService.getExpiringTokens(days);
        return ResponseEntity.ok(tokens);
    }

    /**
     * 批量更新Token状态
     */
    @PutMapping("/batch-update-status")
    public ResponseEntity<Void> batchUpdateTokenStatus(
            @RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        CozeToken.TokenStatus status = CozeToken.TokenStatus.valueOf((String) request.get("status"));
        cozeTokenService.batchUpdateTokenStatus(ids, status);
        return ResponseEntity.ok().build();
    }

    /**
     * 导出Token配置
     */
    @PostMapping("/export")
    public ResponseEntity<String> exportTokenConfig(@RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        String config = cozeTokenService.exportTokenConfig(ids);
        return ResponseEntity.ok(config);
    }

    /**
     * 导入Token配置
     */
    @PostMapping("/import")
    public ResponseEntity<List<CozeToken>> importTokenConfig(@RequestBody Map<String, String> request) {
        String config = request.get("config");
        List<CozeToken> tokens = cozeTokenService.importTokenConfig(config);
        return ResponseEntity.ok(tokens);
    }
}
