package com.lait.entity;

import javax.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 飞书文档实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "feishu_documents")
public class FeishuDocument extends BaseEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 文档标题
     */
    @Column(nullable = false, length = 200)
    private String title;

    /**
     * 飞书文档ID
     */
    @Column(nullable = false, unique = true, length = 100)
    private String docId;

    /**
     * 飞书文档Token
     */
    @Column(length = 200)
    private String docToken;

    /**
     * 文档类型
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private DocumentType docType;

    /**
     * 文档状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private DocumentStatus status;

    /**
     * 文档URL
     */
    @Column(length = 500)
    private String docUrl;

    /**
     * 文档内容 (缓存)
     */
    @Column(columnDefinition = "LONGTEXT")
    private String content;

    /**
     * 文档摘要
     */
    @Column(length = 1000)
    private String summary;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 创建者信息
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creatorId", insertable = false, updatable = false)
    private User creator;

    /**
     * 关联学科ID
     */
    private Long subjectId;

    /**
     * 关联学科信息
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "subjectId", insertable = false, updatable = false)
    private Subject subject;

    /**
     * 文档分类
     */
    @Column(length = 100)
    private String category;

    /**
     * 标签 (JSON数组格式)
     */
    @Column(length = 500)
    private String tags;

    /**
     * 是否公开
     */
    @Column(nullable = false)
    private Boolean isPublic = false;

    /**
     * 访问权限
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private AccessLevel accessLevel;

    /**
     * 最后同步时间
     */
    private LocalDateTime lastSyncAt;

    /**
     * 同步状态
     */
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private SyncStatus syncStatus;

    /**
     * 版本号
     */
    @Column(nullable = false)
    private Integer version = 1;

    /**
     * 查看次数
     */
    @Column(nullable = false)
    private Long viewCount = 0L;

    /**
     * 文档配置 (JSON格式)
     */
    @Column(columnDefinition = "TEXT")
    private String config;

    /**
     * 文档类型枚举
     */
    public enum DocumentType {
        DOC("文档"),
        SHEET("表格"),
        SLIDE("演示文稿"),
        MINDMAP("思维导图"),
        BITABLE("多维表格");

        private final String description;

        DocumentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 文档状态枚举
     */
    public enum DocumentStatus {
        ACTIVE("正常"),
        ARCHIVED("已归档"),
        DELETED("已删除"),
        DRAFT("草稿");

        private final String description;

        DocumentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 访问权限枚举
     */
    public enum AccessLevel {
        PUBLIC("公开"),
        INTERNAL("内部"),
        PRIVATE("私有"),
        RESTRICTED("受限");

        private final String description;

        AccessLevel(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 同步状态枚举
     */
    public enum SyncStatus {
        SYNCED("已同步"),
        PENDING("待同步"),
        SYNCING("同步中"),
        FAILED("同步失败");

        private final String description;

        SyncStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
