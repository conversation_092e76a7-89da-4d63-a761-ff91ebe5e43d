<template>
  <div class="practice-page">
    <!-- 头部 -->
    <van-nav-bar title="练习模块" left-arrow @click-left="$router.go(-1)" />

    <!-- 练习模式选择 -->
    <div class="practice-modes" v-if="!practiceStarted">
      <van-cell-group>
        <van-cell title="按学科练习" is-link @click="showSubjectSelector = true">
          <template #icon>
            <van-icon name="bookmark" />
          </template>
        </van-cell>
        <van-cell title="随机练习" is-link @click="startRandomPractice">
          <template #icon>
            <van-icon name="shuffle" />
          </template>
        </van-cell>
        <van-cell title="错题复习" is-link @click="startWrongQuestionReview">
          <template #icon>
            <van-icon name="warning" />
          </template>
        </van-cell>
        <van-cell title="模拟考试" is-link @click="startMockExam">
          <template #icon>
            <van-icon name="edit" />
          </template>
        </van-cell>
      </van-cell-group>
    </div>

    <!-- 练习进行中 -->
    <div class="practice-content" v-if="practiceStarted">
      <!-- 进度条 -->
      <div class="progress-bar">
        <van-progress :percentage="progress" stroke-width="8" />
        <div class="progress-text">
          {{ currentQuestionIndex + 1 }} / {{ questions.length }}
        </div>
      </div>

      <!-- 题目内容 -->
      <div class="question-card" v-if="currentQuestion">
        <div class="question-header">
          <van-tag :type="getDifficultyColor(currentQuestion.difficulty)">
            {{ getDifficultyText(currentQuestion.difficulty) }}
          </van-tag>
          <van-tag type="primary">{{ currentQuestion.subjectName }}</van-tag>
        </div>

        <div class="question-content">
          <h3>{{ currentQuestion.content }}</h3>
        </div>

        <!-- 选择题选项 -->
        <div class="question-options" v-if="isChoiceQuestion">
          <van-radio-group v-model="userAnswer" v-if="currentQuestion.questionType === 'SINGLE_CHOICE'">
            <van-cell-group>
              <van-cell
                v-for="(option, index) in questionOptions"
                :key="index"
                clickable
                @click="userAnswer = option.value"
              >
                <template #title>
                  <van-radio :name="option.value">{{ option.label }}</van-radio>
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>

          <van-checkbox-group v-model="userAnswerArray" v-if="currentQuestion.questionType === 'MULTIPLE_CHOICE'">
            <van-cell-group>
              <van-cell
                v-for="(option, index) in questionOptions"
                :key="index"
                clickable
                @click="toggleAnswer(option.value)"
              >
                <template #title>
                  <van-checkbox :name="option.value">{{ option.label }}</van-checkbox>
                </template>
              </van-cell>
            </van-cell-group>
          </van-checkbox-group>
        </div>

        <!-- 判断题 -->
        <div class="question-options" v-if="currentQuestion.questionType === 'TRUE_FALSE'">
          <van-radio-group v-model="userAnswer">
            <van-cell-group>
              <van-cell clickable @click="userAnswer = 'true'">
                <template #title>
                  <van-radio name="true">正确</van-radio>
                </template>
              </van-cell>
              <van-cell clickable @click="userAnswer = 'false'">
                <template #title>
                  <van-radio name="false">错误</van-radio>
                </template>
              </van-cell>
            </van-cell-group>
          </van-radio-group>
        </div>

        <!-- 填空题和简答题 -->
        <div class="question-input" v-if="isInputQuestion">
          <van-field
            v-model="userAnswer"
            :placeholder="currentQuestion.questionType === 'FILL_BLANK' ? '请输入答案' : '请输入你的答案'"
            :type="currentQuestion.questionType === 'SHORT_ANSWER' ? 'textarea' : 'text'"
            :rows="4"
            autosize
          />
        </div>

        <!-- 操作按钮 -->
        <div class="question-actions">
          <van-button
            type="default"
            size="large"
            @click="skipQuestion"
            :disabled="!canSkip"
          >
            跳过
          </van-button>
          <van-button
            type="primary"
            size="large"
            @click="submitAnswer"
            :disabled="!hasAnswer"
          >
            提交答案
          </van-button>
        </div>
      </div>

      <!-- 答题结果 -->
      <div class="answer-result" v-if="showResult">
        <van-cell-group>
          <van-cell>
            <template #title>
              <div class="result-header">
                <van-icon
                  :name="isCorrect ? 'success' : 'cross'"
                  :color="isCorrect ? '#07c160' : '#ee0a24'"
                  size="24"
                />
                <span :class="['result-text', isCorrect ? 'correct' : 'wrong']">
                  {{ isCorrect ? '回答正确！' : '回答错误' }}
                </span>
              </div>
            </template>
          </van-cell>
          <van-cell title="正确答案" :value="currentQuestion.correctAnswer" />
          <van-cell title="你的答案" :value="displayUserAnswer" />
          <van-cell title="解析" v-if="currentQuestion.explanation">
            <template #value>
              <div class="explanation">{{ currentQuestion.explanation }}</div>
            </template>
          </van-cell>
        </van-cell-group>

        <div class="result-actions">
          <van-button
            type="warning"
            size="large"
            @click="addToWrongQuestions"
            v-if="!isCorrect"
          >
            加入错题本
          </van-button>
          <van-button
            type="primary"
            size="large"
            @click="nextQuestion"
          >
            {{ isLastQuestion ? '完成练习' : '下一题' }}
          </van-button>
        </div>
      </div>
    </div>

    <!-- 练习完成 -->
    <div class="practice-summary" v-if="practiceCompleted">
      <van-cell-group>
        <van-cell>
          <template #title>
            <div class="summary-header">
              <van-icon name="trophy" color="#ffd21e" size="32" />
              <h2>练习完成！</h2>
            </div>
          </template>
        </van-cell>
        <van-cell title="总题数" :value="questions.length" />
        <van-cell title="正确题数" :value="correctCount" />
        <van-cell title="错误题数" :value="wrongCount" />
        <van-cell title="正确率" :value="`${Math.round((correctCount / questions.length) * 100)}%`" />
        <van-cell title="用时" :value="formatTime(practiceTime)" />
      </van-cell-group>

      <div class="summary-actions">
        <van-button type="default" size="large" @click="restartPractice">
          重新练习
        </van-button>
        <van-button type="primary" size="large" @click="backToHome">
          返回首页
        </van-button>
      </div>
    </div>

    <!-- 学科选择器 -->
    <van-popup v-model:show="showSubjectSelector" position="bottom">
      <van-picker
        :columns="subjectColumns"
        @confirm="onSubjectConfirm"
        @cancel="showSubjectSelector = false"
      />
    </van-popup>

    <!-- 练习设置 -->
    <van-popup v-model:show="showPracticeSettings" position="bottom">
      <div class="practice-settings">
        <van-cell-group>
          <van-field label="题目数量" v-model="practiceSettings.questionCount" type="number" />
          <van-field label="难度">
            <template #input>
              <van-radio-group v-model="practiceSettings.difficulty" direction="horizontal">
                <van-radio name="EASY">简单</van-radio>
                <van-radio name="MEDIUM">中等</van-radio>
                <van-radio name="HARD">困难</van-radio>
                <van-radio name="">全部</van-radio>
              </van-radio-group>
            </template>
          </van-field>
        </van-cell-group>
        <div class="settings-actions">
          <van-button type="default" @click="showPracticeSettings = false">取消</van-button>
          <van-button type="primary" @click="startPracticeWithSettings">开始练习</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { showToast, showConfirmDialog } from 'vant'
import { getSubjects } from '@/api/subjects'
import { getPracticeQuestions, getRandomQuestions, submitAnswer } from '@/api/questions'
import { getMyWrongQuestions } from '@/api/wrongQuestions'
import { createWrongQuestion } from '@/api/wrongQuestions'

const router = useRouter()

// 响应式数据
const practiceStarted = ref(false)
const practiceCompleted = ref(false)
const showResult = ref(false)
const showSubjectSelector = ref(false)
const showPracticeSettings = ref(false)

const questions = ref([])
const currentQuestionIndex = ref(0)
const userAnswer = ref('')
const userAnswerArray = ref([])
const isCorrect = ref(false)
const correctCount = ref(0)
const wrongCount = ref(0)
const practiceStartTime = ref(0)
const practiceTime = ref(0)

const subjects = ref([])
const selectedSubject = ref(null)

// 练习设置
const practiceSettings = reactive({
  questionCount: 10,
  difficulty: '',
  subjectId: null
})

// 计算属性
const currentQuestion = computed(() => {
  return questions.value[currentQuestionIndex.value]
})

const progress = computed(() => {
  if (questions.value.length === 0) return 0
  return Math.round(((currentQuestionIndex.value + 1) / questions.value.length) * 100)
})

const isChoiceQuestion = computed(() => {
  return currentQuestion.value && ['SINGLE_CHOICE', 'MULTIPLE_CHOICE'].includes(currentQuestion.value.questionType)
})

const isInputQuestion = computed(() => {
  return currentQuestion.value && ['FILL_BLANK', 'SHORT_ANSWER'].includes(currentQuestion.value.questionType)
})

const questionOptions = computed(() => {
  if (!currentQuestion.value || !currentQuestion.value.options) return []
  return currentQuestion.value.options.split('\n').map((option, index) => ({
    label: option.trim(),
    value: String.fromCharCode(65 + index) // A, B, C, D...
  }))
})

const hasAnswer = computed(() => {
  if (currentQuestion.value?.questionType === 'MULTIPLE_CHOICE') {
    return userAnswerArray.value.length > 0
  }
  return userAnswer.value.trim() !== ''
})

const canSkip = computed(() => {
  return currentQuestionIndex.value < questions.value.length - 1
})

const isLastQuestion = computed(() => {
  return currentQuestionIndex.value === questions.value.length - 1
})

const displayUserAnswer = computed(() => {
  if (currentQuestion.value?.questionType === 'MULTIPLE_CHOICE') {
    return userAnswerArray.value.join(', ')
  }
  return userAnswer.value
})

const subjectColumns = computed(() => {
  return subjects.value.map(subject => ({
    text: subject.name,
    value: subject.id
  }))
})

// 定时器
let timer = null

// 方法
const loadSubjects = async () => {
  try {
    const response = await getSubjects()
    subjects.value = response.data
  } catch (error) {
    showToast('加载学科列表失败')
  }
}

const startRandomPractice = () => {
  practiceSettings.subjectId = null
  showPracticeSettings.value = true
}

const startWrongQuestionReview = async () => {
  try {
    const response = await getMyWrongQuestions({ isMastered: false })
    if (response.data.content.length === 0) {
      showToast('暂无错题需要复习')
      return
    }
    questions.value = response.data.content.map(item => item.question)
    startPractice()
  } catch (error) {
    showToast('加载错题失败')
  }
}

const startMockExam = () => {
  practiceSettings.questionCount = 20
  practiceSettings.difficulty = ''
  showPracticeSettings.value = true
}

const onSubjectConfirm = (value) => {
  selectedSubject.value = subjects.value.find(s => s.id === value.selectedValues[0])
  practiceSettings.subjectId = value.selectedValues[0]
  showSubjectSelector.value = false
  showPracticeSettings.value = true
}

const startPracticeWithSettings = async () => {
  try {
    let response
    if (practiceSettings.subjectId) {
      response = await getPracticeQuestions({
        subjectId: practiceSettings.subjectId,
        size: practiceSettings.questionCount,
        difficulty: practiceSettings.difficulty || undefined
      })
    } else {
      response = await getRandomQuestions({
        size: practiceSettings.questionCount,
        difficulty: practiceSettings.difficulty || undefined
      })
    }

    questions.value = response.data.content || response.data
    if (questions.value.length === 0) {
      showToast('没有找到符合条件的题目')
      return
    }

    showPracticeSettings.value = false
    startPractice()
  } catch (error) {
    showToast('加载题目失败')
  }
}

const startPractice = () => {
  practiceStarted.value = true
  practiceCompleted.value = false
  currentQuestionIndex.value = 0
  correctCount.value = 0
  wrongCount.value = 0
  practiceStartTime.value = Date.now()

  resetAnswer()
  startTimer()
}

const startTimer = () => {
  timer = setInterval(() => {
    practiceTime.value = Math.floor((Date.now() - practiceStartTime.value) / 1000)
  }, 1000)
}

const stopTimer = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

const resetAnswer = () => {
  userAnswer.value = ''
  userAnswerArray.value = []
  showResult.value = false
}

const toggleAnswer = (value) => {
  const index = userAnswerArray.value.indexOf(value)
  if (index > -1) {
    userAnswerArray.value.splice(index, 1)
  } else {
    userAnswerArray.value.push(value)
  }
}

const skipQuestion = () => {
  if (isLastQuestion.value) {
    completePractice()
  } else {
    currentQuestionIndex.value++
    resetAnswer()
  }
}

const submitAnswer = async () => {
  try {
    const answer = currentQuestion.value.questionType === 'MULTIPLE_CHOICE'
      ? userAnswerArray.value.join(',')
      : userAnswer.value

    const response = await submitAnswer({
      questionId: currentQuestion.value.id,
      answer: answer
    })

    isCorrect.value = response.data.isCorrect
    if (isCorrect.value) {
      correctCount.value++
    } else {
      wrongCount.value++
    }

    showResult.value = true
  } catch (error) {
    showToast('提交答案失败')
  }
}

const addToWrongQuestions = async () => {
  try {
    await createWrongQuestion({
      questionId: currentQuestion.value.id,
      studentAnswer: displayUserAnswer.value
    })
    showToast('已加入错题本')
  } catch (error) {
    showToast('加入错题本失败')
  }
}

const nextQuestion = () => {
  if (isLastQuestion.value) {
    completePractice()
  } else {
    currentQuestionIndex.value++
    resetAnswer()
  }
}

const completePractice = () => {
  practiceStarted.value = false
  practiceCompleted.value = true
  stopTimer()
}

const restartPractice = () => {
  practiceCompleted.value = false
  startPractice()
}

const backToHome = () => {
  router.push('/home')
}

// 辅助方法
const getDifficultyText = (difficulty) => {
  const map = {
    'EASY': '简单',
    'MEDIUM': '中等',
    'HARD': '困难'
  }
  return map[difficulty] || difficulty
}

const getDifficultyColor = (difficulty) => {
  const map = {
    'EASY': 'success',
    'MEDIUM': 'warning',
    'HARD': 'danger'
  }
  return map[difficulty] || 'default'
}

const formatTime = (seconds) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}分${remainingSeconds}秒`
}

// 生命周期
onMounted(() => {
  loadSubjects()
})

onUnmounted(() => {
  stopTimer()
})
</script>

<style scoped>
.practice-page {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.practice-modes {
  padding: 16px;
}

.practice-content {
  padding: 16px;
}

.progress-bar {
  margin-bottom: 20px;
  background: white;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-text {
  text-align: center;
  margin-top: 8px;
  font-size: 14px;
  color: #646566;
}

.question-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.question-header {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.question-content h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  line-height: 1.6;
  color: #323233;
}

.question-options {
  margin: 20px 0;
}

.question-input {
  margin: 20px 0;
}

.question-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.question-actions .van-button {
  flex: 1;
}

.answer-result {
  margin-top: 20px;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.result-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-text {
  font-weight: bold;
  font-size: 16px;
}

.result-text.correct {
  color: #07c160;
}

.result-text.wrong {
  color: #ee0a24;
}

.explanation {
  font-size: 14px;
  line-height: 1.6;
  color: #646566;
}

.result-actions {
  display: flex;
  gap: 12px;
  padding: 16px;
}

.result-actions .van-button {
  flex: 1;
}

.practice-summary {
  padding: 16px;
}

.summary-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.summary-header h2 {
  margin: 0;
  color: #323233;
}

.summary-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.summary-actions .van-button {
  flex: 1;
}

.practice-settings {
  padding: 20px;
}

.settings-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.settings-actions .van-button {
  flex: 1;
}

.van-cell-group {
  margin-bottom: 16px;
}

.van-cell {
  padding: 16px;
}

.van-radio-group .van-cell {
  padding: 12px 16px;
}

.van-checkbox-group .van-cell {
  padding: 12px 16px;
}

@media (max-width: 375px) {
  .practice-content {
    padding: 12px;
  }

  .question-card {
    padding: 16px;
  }

  .question-actions {
    flex-direction: column;
  }

  .result-actions {
    flex-direction: column;
  }

  .summary-actions {
    flex-direction: column;
  }
}
</style>
