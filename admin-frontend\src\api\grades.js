import request from './request'

// 获取成绩列表
export function getGrades(params) {
  return request({
    url: '/grades',
    method: 'get',
    params
  })
}

// 获取成绩详情
export function getGrade(id) {
  return request({
    url: `/grades/${id}`,
    method: 'get'
  })
}

// 创建成绩
export function createGrade(data) {
  return request({
    url: '/grades',
    method: 'post',
    data
  })
}

// 更新成绩
export function updateGrade(id, data) {
  return request({
    url: `/grades/${id}`,
    method: 'put',
    data
  })
}

// 删除成绩
export function deleteGrade(id) {
  return request({
    url: `/grades/${id}`,
    method: 'delete'
  })
}

// 批量删除成绩
export function batchDeleteGrades(ids) {
  return request({
    url: '/grades/batch',
    method: 'delete',
    data: { ids }
  })
}

// 根据学生获取成绩
export function getGradesByStudent(studentId, params) {
  return request({
    url: `/grades/student/${studentId}`,
    method: 'get',
    params
  })
}

// 根据学科获取成绩
export function getGradesBySubject(subjectId, params) {
  return request({
    url: `/grades/subject/${subjectId}`,
    method: 'get',
    params
  })
}

// 获取成绩统计信息
export function getGradeStats() {
  return request({
    url: '/grades/stats',
    method: 'get'
  })
}

// 导出成绩
export function exportGrades(params) {
  return request({
    url: '/grades/export',
    method: 'get',
    params,
    responseType: 'blob'
  })
}
