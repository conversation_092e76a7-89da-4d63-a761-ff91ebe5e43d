-- 智能学习系统数据库表结构
-- 版本: 2.0
-- 更新时间: 2024-01-15
-- 兼容性: MySQL 8.0+, Java 8

-- 设置字符集和排序规则
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 用户表 (增强版)
CREATE TABLE IF NOT EXISTS users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(BCrypt加密)',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    real_name VARCHAR(100) COMMENT '真实姓名',
    phone_number VARCHAR(20) COMMENT '手机号码',
    role ENUM('ADMIN', 'TEACHER', 'STUDENT') NOT NULL COMMENT '用户角色',
    status ENUM('ACTIVE', 'INACTIVE', 'SUSPENDED') NOT NULL DEFAULT 'ACTIVE' COMMENT '用户状态',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    last_login_time DATETIME COMMENT '最后登录时间',
    last_login_ip VARCHAR(45) COMMENT '最后登录IP',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    grade_level INT COMMENT '年级(1-12)',
    class_name VARCHAR(50) COMMENT '班级名称',
    school_name VARCHAR(100) COMMENT '学校名称',
    student_number VARCHAR(50) COMMENT '学号',
    parent_phone VARCHAR(20) COMMENT '家长联系电话',
    birthday DATE COMMENT '生日',
    gender ENUM('MALE', 'FEMALE', 'OTHER') COMMENT '性别',
    address TEXT COMMENT '地址',
    emergency_contact VARCHAR(100) COMMENT '紧急联系人',
    emergency_phone VARCHAR(20) COMMENT '紧急联系电话',
    preferences JSON COMMENT '用户偏好设置',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_phone (phone_number),
    INDEX idx_role (role),
    INDEX idx_status (status),
    INDEX idx_grade_level (grade_level),
    INDEX idx_class_name (class_name),
    INDEX idx_school_name (school_name),
    INDEX idx_student_number (student_number),
    INDEX idx_created_time (created_time),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 学科表 (增强版)
CREATE TABLE IF NOT EXISTS subjects (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '学科ID',
    name VARCHAR(100) NOT NULL COMMENT '学科名称',
    description TEXT COMMENT '学科描述',
    grade_level INT COMMENT '适用年级(1-12)',
    subject_code VARCHAR(20) UNIQUE COMMENT '学科代码',
    status ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '学科状态',
    sort_order INT DEFAULT 0 COMMENT '排序',
    icon_url VARCHAR(500) COMMENT '学科图标URL',
    color VARCHAR(7) DEFAULT '#1890ff' COMMENT '主题颜色',
    total_hours INT DEFAULT 0 COMMENT '总课时',
    credit_hours DECIMAL(3,1) DEFAULT 0.0 COMMENT '学分',
    difficulty_level ENUM('BEGINNER', 'INTERMEDIATE', 'ADVANCED') DEFAULT 'BEGINNER' COMMENT '难度等级',
    prerequisites TEXT COMMENT '前置课程要求',
    learning_objectives TEXT COMMENT '学习目标',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_subject_code (subject_code),
    INDEX idx_grade_level (grade_level),
    INDEX idx_status (status),
    INDEX idx_sort_order (sort_order),
    INDEX idx_difficulty_level (difficulty_level),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学科表';

-- 章节表
CREATE TABLE IF NOT EXISTS chapters (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '章节ID',
    subject_id BIGINT NOT NULL COMMENT '学科ID',
    parent_id BIGINT COMMENT '父章节ID',
    name VARCHAR(200) NOT NULL COMMENT '章节名称',
    description TEXT COMMENT '章节描述',
    chapter_number VARCHAR(20) COMMENT '章节编号',
    sort_order INT DEFAULT 0 COMMENT '排序',
    level INT DEFAULT 1 COMMENT '层级(1-5)',
    status ENUM('ACTIVE', 'INACTIVE') DEFAULT 'ACTIVE' COMMENT '状态',
    learning_objectives TEXT COMMENT '学习目标',
    estimated_hours DECIMAL(4,2) DEFAULT 0.00 COMMENT '预计学时',
    difficulty_level ENUM('EASY', 'MEDIUM', 'HARD') DEFAULT 'MEDIUM' COMMENT '难度等级',
    prerequisites TEXT COMMENT '前置知识',
    resources JSON COMMENT '学习资源(JSON格式)',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_subject_id (subject_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_level (level),
    INDEX idx_status (status),
    INDEX idx_is_deleted (is_deleted),
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES chapters(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='章节表';

-- 题目表 (增强版)
CREATE TABLE IF NOT EXISTS questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '题目ID',
    subject_id BIGINT NOT NULL COMMENT '学科ID',
    chapter_id BIGINT COMMENT '章节ID',
    content TEXT NOT NULL COMMENT '题目内容',
    question_type ENUM('SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'TRUE_FALSE', 'FILL_BLANK', 'SHORT_ANSWER', 'ESSAY', 'CALCULATION') NOT NULL COMMENT '题目类型',
    options JSON COMMENT '选项(JSON格式)',
    correct_answer TEXT NOT NULL COMMENT '正确答案',
    explanation TEXT COMMENT '答案解析',
    difficulty ENUM('EASY', 'MEDIUM', 'HARD') NOT NULL COMMENT '难度等级',
    grade_level INT COMMENT '适用年级',
    tags VARCHAR(500) COMMENT '标签(逗号分隔)',
    knowledge_points TEXT COMMENT '知识点',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    correct_count INT DEFAULT 0 COMMENT '正确次数',
    wrong_count INT DEFAULT 0 COMMENT '错误次数',
    average_time INT DEFAULT 0 COMMENT '平均答题时间(秒)',
    points INT DEFAULT 1 COMMENT '题目分值',
    source VARCHAR(100) COMMENT '题目来源',
    source_url VARCHAR(500) COMMENT '来源链接',
    images JSON COMMENT '图片URLs(JSON数组)',
    audio_url VARCHAR(500) COMMENT '音频URL',
    video_url VARCHAR(500) COMMENT '视频URL',
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') DEFAULT 'DRAFT' COMMENT '题目状态',
    quality_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '质量评分(0-5)',
    review_status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING' COMMENT '审核状态',
    reviewer_id BIGINT COMMENT '审核人ID',
    review_time DATETIME COMMENT '审核时间',
    review_comments TEXT COMMENT '审核意见',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_subject_id (subject_id),
    INDEX idx_chapter_id (chapter_id),
    INDEX idx_question_type (question_type),
    INDEX idx_difficulty (difficulty),
    INDEX idx_grade_level (grade_level),
    INDEX idx_status (status),
    INDEX idx_review_status (review_status),
    INDEX idx_quality_score (quality_score),
    INDEX idx_created_time (created_time),
    INDEX idx_is_deleted (is_deleted),
    FULLTEXT idx_content (content),
    FULLTEXT idx_tags (tags),
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='题目表';

-- 成绩表 (增强版)
CREATE TABLE IF NOT EXISTS grades (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '成绩ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    subject_id BIGINT NOT NULL COMMENT '学科ID',
    exam_id BIGINT COMMENT '考试ID',
    exam_name VARCHAR(100) COMMENT '考试名称',
    exam_type ENUM('QUIZ', 'MIDTERM', 'FINAL', 'HOMEWORK', 'PROJECT', 'PRACTICE') COMMENT '考试类型',
    exam_date DATE COMMENT '考试日期',
    score DECIMAL(5,2) NOT NULL COMMENT '得分',
    total_score DECIMAL(5,2) DEFAULT 100.00 COMMENT '总分',
    percentage DECIMAL(5,2) COMMENT '得分率',
    grade_letter VARCHAR(5) COMMENT '等级(A+, A, B+, B, C+, C, D, F)',
    class_rank INT COMMENT '班级排名',
    grade_rank INT COMMENT '年级排名',
    class_average DECIMAL(5,2) COMMENT '班级平均分',
    grade_average DECIMAL(5,2) COMMENT '年级平均分',
    improvement DECIMAL(5,2) COMMENT '较上次提升分数',
    time_spent INT COMMENT '答题用时(分钟)',
    attempt_count INT DEFAULT 1 COMMENT '尝试次数',
    comments TEXT COMMENT '评语',
    teacher_feedback TEXT COMMENT '教师反馈',
    strengths TEXT COMMENT '优势分析',
    weaknesses TEXT COMMENT '薄弱环节',
    suggestions TEXT COMMENT '改进建议',
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') NOT NULL DEFAULT 'DRAFT' COMMENT '状态',
    is_makeup BOOLEAN DEFAULT FALSE COMMENT '是否补考',
    makeup_reason VARCHAR(200) COMMENT '补考原因',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_student_id (student_id),
    INDEX idx_subject_id (subject_id),
    INDEX idx_exam_id (exam_id),
    INDEX idx_exam_type (exam_type),
    INDEX idx_exam_date (exam_date),
    INDEX idx_status (status),
    INDEX idx_score (score),
    INDEX idx_percentage (percentage),
    INDEX idx_created_time (created_time),
    INDEX idx_is_deleted (is_deleted),
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='成绩表';

-- 错题表 (增强版)
CREATE TABLE IF NOT EXISTS wrong_questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '错题ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    exam_id BIGINT COMMENT '考试ID',
    student_answer TEXT COMMENT '学生答案',
    correct_answer TEXT COMMENT '正确答案',
    wrong_count INT DEFAULT 1 COMMENT '错误次数',
    review_count INT DEFAULT 0 COMMENT '复习次数',
    is_mastered BOOLEAN DEFAULT FALSE COMMENT '是否掌握',
    mastered_time DATETIME COMMENT '掌握时间',
    difficulty_rating INT DEFAULT 3 COMMENT '难度评级(1-5)',
    importance_level ENUM('LOW', 'MEDIUM', 'HIGH', 'CRITICAL') DEFAULT 'MEDIUM' COMMENT '重要程度',
    error_type ENUM('CONCEPT', 'CALCULATION', 'CARELESS', 'METHOD', 'KNOWLEDGE') COMMENT '错误类型',
    error_reason TEXT COMMENT '错误原因分析',
    notes TEXT COMMENT '学生笔记',
    teacher_notes TEXT COMMENT '教师备注',
    review_status ENUM('PENDING', 'REVIEWING', 'MASTERED') NOT NULL DEFAULT 'PENDING' COMMENT '复习状态',
    next_review_time DATETIME COMMENT '下次复习时间',
    review_interval INT DEFAULT 1 COMMENT '复习间隔(天)',
    last_review_time DATETIME COMMENT '最后复习时间',
    review_performance DECIMAL(3,2) COMMENT '复习表现评分',
    knowledge_points VARCHAR(500) COMMENT '相关知识点',
    similar_questions JSON COMMENT '相似题目ID列表',
    practice_suggestions TEXT COMMENT '练习建议',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_student_id (student_id),
    INDEX idx_question_id (question_id),
    INDEX idx_exam_id (exam_id),
    INDEX idx_review_status (review_status),
    INDEX idx_is_mastered (is_mastered),
    INDEX idx_error_type (error_type),
    INDEX idx_importance_level (importance_level),
    INDEX idx_next_review_time (next_review_time),
    INDEX idx_created_time (created_time),
    INDEX idx_is_deleted (is_deleted),
    UNIQUE KEY uk_student_question (student_id, question_id),
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='错题表';

-- 笔记表 (增强版)
CREATE TABLE IF NOT EXISTS notes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '笔记ID',
    student_id BIGINT NOT NULL COMMENT '学生ID',
    subject_id BIGINT COMMENT '学科ID',
    chapter_id BIGINT COMMENT '章节ID',
    question_id BIGINT COMMENT '关联题目ID',
    title VARCHAR(200) NOT NULL COMMENT '笔记标题',
    content LONGTEXT COMMENT '笔记内容',
    summary TEXT COMMENT '笔记摘要',
    tags VARCHAR(500) COMMENT '标签(逗号分隔)',
    note_type ENUM('STUDY', 'REVIEW', 'SUMMARY', 'ERROR', 'INSPIRATION', 'QUESTION') NOT NULL COMMENT '笔记类型',
    format_type ENUM('TEXT', 'MARKDOWN', 'HTML', 'RICH_TEXT') DEFAULT 'TEXT' COMMENT '格式类型',
    status ENUM('DRAFT', 'PUBLISHED', 'ARCHIVED') NOT NULL DEFAULT 'DRAFT' COMMENT '状态',
    priority ENUM('LOW', 'MEDIUM', 'HIGH') DEFAULT 'MEDIUM' COMMENT '优先级',
    difficulty_level ENUM('EASY', 'MEDIUM', 'HARD') COMMENT '难度等级',
    quality_score DECIMAL(3,2) DEFAULT 0.00 COMMENT '质量评分(0-5)',
    feishu_doc_id VARCHAR(100) COMMENT '飞书文档ID',
    feishu_url VARCHAR(500) COMMENT '飞书文档链接',
    is_shared BOOLEAN DEFAULT FALSE COMMENT '是否分享',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    is_favorite BOOLEAN DEFAULT FALSE COMMENT '是否收藏',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    like_count INT DEFAULT 0 COMMENT '点赞次数',
    comment_count INT DEFAULT 0 COMMENT '评论次数',
    share_count INT DEFAULT 0 COMMENT '分享次数',
    word_count INT DEFAULT 0 COMMENT '字数统计',
    reading_time INT DEFAULT 0 COMMENT '预计阅读时间(分钟)',
    attachments JSON COMMENT '附件列表(JSON格式)',
    images JSON COMMENT '图片列表(JSON格式)',
    links JSON COMMENT '相关链接(JSON格式)',
    references TEXT COMMENT '参考资料',
    knowledge_points VARCHAR(500) COMMENT '知识点',
    learning_objectives TEXT COMMENT '学习目标',
    key_concepts TEXT COMMENT '关键概念',
    examples TEXT COMMENT '示例',
    exercises TEXT COMMENT '练习题',
    reflection TEXT COMMENT '学习反思',
    improvement_plan TEXT COMMENT '改进计划',
    last_reviewed_time DATETIME COMMENT '最后复习时间',
    review_count INT DEFAULT 0 COMMENT '复习次数',
    sync_status ENUM('SYNCED', 'PENDING', 'FAILED') DEFAULT 'PENDING' COMMENT '同步状态',
    sync_time DATETIME COMMENT '同步时间',
    version INT DEFAULT 1 COMMENT '版本号',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_student_id (student_id),
    INDEX idx_subject_id (subject_id),
    INDEX idx_chapter_id (chapter_id),
    INDEX idx_question_id (question_id),
    INDEX idx_note_type (note_type),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_is_shared (is_shared),
    INDEX idx_is_public (is_public),
    INDEX idx_quality_score (quality_score),
    INDEX idx_created_time (created_time),
    INDEX idx_last_reviewed_time (last_reviewed_time),
    INDEX idx_is_deleted (is_deleted),
    FULLTEXT idx_title (title),
    FULLTEXT idx_content (content),
    FULLTEXT idx_tags (tags),
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE SET NULL,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE SET NULL,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='笔记表';

-- 考试表
CREATE TABLE IF NOT EXISTS exams (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '考试ID',
    name VARCHAR(200) NOT NULL COMMENT '考试名称',
    description TEXT COMMENT '考试描述',
    subject_id BIGINT NOT NULL COMMENT '学科ID',
    exam_type ENUM('QUIZ', 'MIDTERM', 'FINAL', 'HOMEWORK', 'PROJECT', 'PRACTICE') NOT NULL COMMENT '考试类型',
    difficulty_level ENUM('EASY', 'MEDIUM', 'HARD') DEFAULT 'MEDIUM' COMMENT '难度等级',
    total_score DECIMAL(5,2) DEFAULT 100.00 COMMENT '总分',
    passing_score DECIMAL(5,2) DEFAULT 60.00 COMMENT '及格分',
    duration INT COMMENT '考试时长(分钟)',
    question_count INT DEFAULT 0 COMMENT '题目数量',
    start_time DATETIME COMMENT '开始时间',
    end_time DATETIME COMMENT '结束时间',
    status ENUM('DRAFT', 'PUBLISHED', 'ACTIVE', 'COMPLETED', 'CANCELLED') DEFAULT 'DRAFT' COMMENT '考试状态',
    is_random_order BOOLEAN DEFAULT FALSE COMMENT '是否随机排序',
    is_show_result BOOLEAN DEFAULT TRUE COMMENT '是否显示结果',
    is_allow_review BOOLEAN DEFAULT TRUE COMMENT '是否允许复查',
    max_attempts INT DEFAULT 1 COMMENT '最大尝试次数',
    instructions TEXT COMMENT '考试说明',
    grade_levels JSON COMMENT '适用年级(JSON数组)',
    class_names JSON COMMENT '适用班级(JSON数组)',
    teacher_id BIGINT COMMENT '出题教师ID',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_subject_id (subject_id),
    INDEX idx_exam_type (exam_type),
    INDEX idx_status (status),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_created_time (created_time),
    INDEX idx_is_deleted (is_deleted),
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试表';

-- 考试题目关联表
CREATE TABLE IF NOT EXISTS exam_questions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联ID',
    exam_id BIGINT NOT NULL COMMENT '考试ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    sort_order INT DEFAULT 0 COMMENT '题目顺序',
    points DECIMAL(5,2) DEFAULT 1.00 COMMENT '题目分值',
    is_required BOOLEAN DEFAULT TRUE COMMENT '是否必答',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_exam_id (exam_id),
    INDEX idx_question_id (question_id),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_deleted (is_deleted),
    UNIQUE KEY uk_exam_question (exam_id, question_id),
    FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='考试题目关联表';

-- Coze Token管理表
CREATE TABLE IF NOT EXISTS coze_tokens (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT 'Token ID',
    token_name VARCHAR(100) NOT NULL COMMENT 'Token名称',
    token_value VARCHAR(500) NOT NULL COMMENT 'Token值',
    description TEXT COMMENT '描述',
    api_endpoint VARCHAR(200) COMMENT 'API端点',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    usage_count INT DEFAULT 0 COMMENT '使用次数',
    daily_limit INT DEFAULT 1000 COMMENT '每日限制',
    monthly_limit INT DEFAULT 30000 COMMENT '每月限制',
    last_used_time DATETIME COMMENT '最后使用时间',
    expires_at DATETIME COMMENT '过期时间',
    rate_limit_per_minute INT DEFAULT 60 COMMENT '每分钟限制',
    priority INT DEFAULT 1 COMMENT '优先级(1-10)',
    environment ENUM('DEVELOPMENT', 'TESTING', 'PRODUCTION') DEFAULT 'PRODUCTION' COMMENT '环境',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_token_name (token_name),
    INDEX idx_is_active (is_active),
    INDEX idx_environment (environment),
    INDEX idx_priority (priority),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Coze Token管理表';

-- 积分配置表
CREATE TABLE IF NOT EXISTS points_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
    points INT NOT NULL COMMENT '积分值',
    category ENUM('LEARNING', 'PRACTICE', 'SOCIAL', 'ACHIEVEMENT', 'PENALTY') NOT NULL COMMENT '配置分类',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    daily_limit INT DEFAULT 0 COMMENT '每日限制次数(0表示无限制)',
    description VARCHAR(500) COMMENT '描述',
    sort_order INT DEFAULT 0 COMMENT '排序',
    conditions JSON COMMENT '触发条件(JSON格式)',
    multiplier DECIMAL(3,2) DEFAULT 1.00 COMMENT '倍数',
    min_points INT COMMENT '最小积分',
    max_points INT COMMENT '最大积分',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_config_key (config_key),
    INDEX idx_category (category),
    INDEX idx_enabled (enabled),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='积分配置表';

-- 用户积分记录表
CREATE TABLE IF NOT EXISTS user_points (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    action_type VARCHAR(100) NOT NULL COMMENT '行为类型',
    points INT NOT NULL COMMENT '积分变化',
    balance INT NOT NULL COMMENT '积分余额',
    description VARCHAR(500) COMMENT '描述',
    reference_type VARCHAR(50) COMMENT '关联类型(question, exam, note等)',
    reference_id BIGINT COMMENT '关联ID',
    source ENUM('SYSTEM', 'MANUAL', 'IMPORT') DEFAULT 'SYSTEM' COMMENT '来源',
    operator_id BIGINT COMMENT '操作人ID',
    expired_at DATETIME COMMENT '过期时间',
    status ENUM('PENDING', 'COMPLETED', 'CANCELLED', 'EXPIRED') DEFAULT 'COMPLETED' COMMENT '状态',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_user_id (user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_reference_type_id (reference_type, reference_id),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time),
    INDEX idx_is_deleted (is_deleted),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户积分记录表';

-- 飞书文档表
CREATE TABLE IF NOT EXISTS feishu_documents (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '文档ID',
    doc_id VARCHAR(100) NOT NULL UNIQUE COMMENT '飞书文档ID',
    title VARCHAR(200) NOT NULL COMMENT '文档标题',
    url VARCHAR(500) COMMENT '文档链接',
    doc_type ENUM('DOC', 'SHEET', 'SLIDE', 'MINDNOTE', 'BITABLE') NOT NULL COMMENT '文档类型',
    owner_id BIGINT COMMENT '所有者ID',
    folder_id VARCHAR(100) COMMENT '文件夹ID',
    is_shared BOOLEAN DEFAULT FALSE COMMENT '是否分享',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    permission ENUM('READ', 'comment', 'edit', 'full_access') DEFAULT 'read' COMMENT '权限级别',
    content_summary TEXT COMMENT '内容摘要',
    word_count INT DEFAULT 0 COMMENT '字数',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    comment_count INT DEFAULT 0 COMMENT '评论数',
    last_modified_time DATETIME COMMENT '最后修改时间',
    synced_at DATETIME COMMENT '同步时间',
    sync_status ENUM('SYNCED', 'PENDING', 'FAILED') DEFAULT 'PENDING' COMMENT '同步状态',
    sync_error TEXT COMMENT '同步错误信息',
    tags VARCHAR(500) COMMENT '标签',
    metadata JSON COMMENT '元数据(JSON格式)',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_doc_id (doc_id),
    INDEX idx_owner_id (owner_id),
    INDEX idx_doc_type (doc_type),
    INDEX idx_sync_status (sync_status),
    INDEX idx_is_shared (is_shared),
    INDEX idx_created_time (created_time),
    INDEX idx_is_deleted (is_deleted),
    FULLTEXT idx_title (title),
    FULLTEXT idx_content_summary (content_summary),
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='飞书文档表';

-- 学习记录表
CREATE TABLE IF NOT EXISTS learning_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    subject_id BIGINT COMMENT '学科ID',
    chapter_id BIGINT COMMENT '章节ID',
    question_id BIGINT COMMENT '题目ID',
    activity_type ENUM('STUDY', 'PRACTICE', 'EXAM', 'REVIEW', 'NOTE') NOT NULL COMMENT '活动类型',
    duration INT DEFAULT 0 COMMENT '学习时长(秒)',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比',
    score DECIMAL(5,2) COMMENT '得分',
    status ENUM('STARTED', 'IN_PROGRESS', 'COMPLETED', 'PAUSED') DEFAULT 'STARTED' COMMENT '状态',
    device_type ENUM('WEB', 'MOBILE', 'TABLET', 'APP') COMMENT '设备类型',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    session_id VARCHAR(100) COMMENT '会话ID',
    metadata JSON COMMENT '元数据',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_user_id (user_id),
    INDEX idx_subject_id (subject_id),
    INDEX idx_chapter_id (chapter_id),
    INDEX idx_question_id (question_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time),
    INDEX idx_is_deleted (is_deleted),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE SET NULL,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE SET NULL,
    FOREIGN KEY (question_id) REFERENCES questions(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习记录表';

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('STRING', 'INTEGER', 'DECIMAL', 'BOOLEAN', 'JSON') DEFAULT 'STRING' COMMENT '配置类型',
    category VARCHAR(50) DEFAULT 'GENERAL' COMMENT '配置分类',
    description VARCHAR(500) COMMENT '描述',
    is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
    is_editable BOOLEAN DEFAULT TRUE COMMENT '是否可编辑',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by BIGINT COMMENT '创建人ID',
    updated_by BIGINT COMMENT '更新人ID',
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否删除',
    INDEX idx_config_key (config_key),
    INDEX idx_category (category),
    INDEX idx_is_public (is_public),
    INDEX idx_sort_order (sort_order),
    INDEX idx_is_deleted (is_deleted)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT COMMENT '操作用户ID',
    operation_type VARCHAR(50) NOT NULL COMMENT '操作类型',
    operation_name VARCHAR(100) NOT NULL COMMENT '操作名称',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id BIGINT COMMENT '资源ID',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_url VARCHAR(500) COMMENT '请求URL',
    request_params TEXT COMMENT '请求参数',
    response_status INT COMMENT '响应状态码',
    response_message TEXT COMMENT '响应消息',
    execution_time INT COMMENT '执行时间(毫秒)',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    success BOOLEAN DEFAULT TRUE COMMENT '是否成功',
    error_message TEXT COMMENT '错误信息',
    created_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_id (user_id),
    INDEX idx_operation_type (operation_type),
    INDEX idx_resource_type_id (resource_type, resource_id),
    INDEX idx_success (success),
    INDEX idx_created_time (created_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- 设置外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 插入初始数据
INSERT INTO users (username, password, email, real_name, role, status, grade_level, class_name) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFVMLkxNlBX2qJ.q.kJtGRu', '<EMAIL>', '系统管理员', 'ADMIN', 'ACTIVE', NULL, NULL),
('teacher1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFVMLkxNlBX2qJ.q.kJtGRu', '<EMAIL>', '张老师', 'TEACHER', 'ACTIVE', NULL, NULL),
('student1', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iYqiSfFVMLkxNlBX2qJ.q.kJtGRu', '<EMAIL>', '李小明', 'STUDENT', 'ACTIVE', 3, '三年级一班');

INSERT INTO subjects (name, description, subject_code, grade_level, status, sort_order, color, icon_url) VALUES
('语文', '小学语文课程，培养学生的语言文字运用能力', 'CHINESE', 1, 'ACTIVE', 1, '#ff4d4f', '/icons/chinese.svg'),
('数学', '小学数学课程，培养学生的数学思维和计算能力', 'MATH', 1, 'ACTIVE', 2, '#1890ff', '/icons/math.svg'),
('英语', '小学英语课程，培养学生的英语听说读写能力', 'ENGLISH', 1, 'ACTIVE', 3, '#52c41a', '/icons/english.svg'),
('科学', '小学科学课程，培养学生的科学探究能力', 'SCIENCE', 1, 'ACTIVE', 4, '#722ed1', '/icons/science.svg'),
('体育', '小学体育课程，增强学生体质', 'PE', 1, 'ACTIVE', 5, '#fa8c16', '/icons/pe.svg'),
('音乐', '小学音乐课程，培养学生的音乐素养', 'MUSIC', 1, 'ACTIVE', 6, '#eb2f96', '/icons/music.svg'),
('美术', '小学美术课程，培养学生的艺术创造能力', 'ART', 1, 'ACTIVE', 7, '#13c2c2', '/icons/art.svg');

-- 插入章节数据
INSERT INTO chapters (subject_id, name, description, chapter_number, sort_order, level, status) VALUES
(1, '拼音学习', '学习汉语拼音的基础知识', '1.1', 1, 1, 'ACTIVE'),
(1, '识字写字', '学习常用汉字的识别和书写', '1.2', 2, 1, 'ACTIVE'),
(1, '阅读理解', '培养阅读理解能力', '1.3', 3, 1, 'ACTIVE'),
(2, '数与代数', '学习数字和基本代数概念', '2.1', 1, 1, 'ACTIVE'),
(2, '图形与几何', '学习基本几何图形', '2.2', 2, 1, 'ACTIVE'),
(2, '统计与概率', '学习简单的统计和概率知识', '2.3', 3, 1, 'ACTIVE'),
(3, '字母学习', '学习英语字母', '3.1', 1, 1, 'ACTIVE'),
(3, '单词学习', '学习基础英语单词', '3.2', 2, 1, 'ACTIVE'),
(3, '简单对话', '学习简单的英语对话', '3.3', 3, 1, 'ACTIVE');

-- 插入积分配置数据
INSERT INTO points_config (config_key, display_name, points, category, enabled, daily_limit, description, sort_order) VALUES
('login_daily', '每日登录', 10, 'LEARNING', TRUE, 1, '每天首次登录获得积分', 1),
('complete_question', '完成题目', 5, 'PRACTICE', TRUE, 0, '每完成一道题目获得积分', 2),
('correct_answer', '答题正确', 10, 'PRACTICE', TRUE, 0, '答对题目额外获得积分', 3),
('create_note', '创建笔记', 20, 'LEARNING', TRUE, 5, '创建学习笔记获得积分', 4),
('share_note', '分享笔记', 15, 'SOCIAL', TRUE, 3, '分享笔记给其他同学获得积分', 5),
('review_wrong_question', '复习错题', 8, 'PRACTICE', TRUE, 0, '复习错题获得积分', 6),
('master_wrong_question', '掌握错题', 25, 'ACHIEVEMENT', TRUE, 0, '完全掌握错题获得积分', 7),
('continuous_study', '连续学习', 50, 'ACHIEVEMENT', TRUE, 1, '连续学习7天获得积分', 8),
('exam_excellent', '考试优秀', 100, 'ACHIEVEMENT', TRUE, 0, '考试成绩优秀(90分以上)获得积分', 9),
('late_submission', '迟交作业', -10, 'PENALTY', TRUE, 0, '迟交作业扣除积分', 10);

-- 插入系统配置数据
INSERT INTO system_config (config_key, config_value, config_type, category, description, is_public, is_editable, sort_order) VALUES
('system_name', 'LAIT智能学习系统', 'STRING', 'GENERAL', '系统名称', TRUE, TRUE, 1),
('system_version', '2.0.0', 'STRING', 'GENERAL', '系统版本', TRUE, FALSE, 2),
('max_upload_size', '10485760', 'INTEGER', 'UPLOAD', '最大上传文件大小(字节)', FALSE, TRUE, 3),
('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx","xls","xlsx","ppt","pptx"]', 'JSON', 'UPLOAD', '允许上传的文件类型', FALSE, TRUE, 4),
('session_timeout', '3600', 'INTEGER', 'SECURITY', '会话超时时间(秒)', FALSE, TRUE, 5),
('password_min_length', '6', 'INTEGER', 'SECURITY', '密码最小长度', FALSE, TRUE, 6),
('enable_registration', 'true', 'BOOLEAN', 'USER', '是否允许用户注册', FALSE, TRUE, 7),
('default_points', '100', 'INTEGER', 'POINTS', '新用户默认积分', FALSE, TRUE, 8),
('points_exchange_rate', '1.0', 'DECIMAL', 'POINTS', '积分兑换比例', FALSE, TRUE, 9),
('maintenance_mode', 'false', 'BOOLEAN', 'SYSTEM', '维护模式', FALSE, TRUE, 10);

-- 插入示例题目数据
INSERT INTO questions (subject_id, chapter_id, content, question_type, options, correct_answer, explanation, difficulty, grade_level, tags, knowledge_points, points, status, review_status) VALUES
(2, 4, '计算：3 + 5 = ?', 'SINGLE_CHOICE', '["6", "7", "8", "9"]', '8', '3加5等于8，这是基本的加法运算。', 'EASY', 1, '加法,基础运算', '整数加法', 1, 'PUBLISHED', 'APPROVED'),
(2, 4, '计算：12 - 7 = ?', 'SINGLE_CHOICE', '["4", "5", "6", "7"]', '5', '12减7等于5，这是基本的减法运算。', 'EASY', 1, '减法,基础运算', '整数减法', 1, 'PUBLISHED', 'APPROVED'),
(1, 1, '下列哪个是正确的拼音？', 'SINGLE_CHOICE', '["bá", "pá", "má", "dá"]', 'má', '妈妈的"妈"字拼音是má，第二声。', 'EASY', 1, '拼音,声调', '拼音识别', 1, 'PUBLISHED', 'APPROVED'),
(3, 7, 'What color is the sky?', 'SINGLE_CHOICE', '["Red", "Blue", "Green", "Yellow"]', 'Blue', 'The sky is blue during a clear day.', 'EASY', 1, 'colors,basic', '颜色词汇', 1, 'PUBLISHED', 'APPROVED'),
(2, 5, '一个正方形有几条边？', 'SINGLE_CHOICE', '["3", "4", "5", "6"]', '4', '正方形有4条相等的边。', 'EASY', 1, '几何,图形', '平面图形', 1, 'PUBLISHED', 'APPROVED');

-- 创建数据库视图
CREATE VIEW v_user_statistics AS
SELECT
    u.id,
    u.username,
    u.real_name,
    u.role,
    u.grade_level,
    u.class_name,
    COALESCE(SUM(up.points), 0) as total_points,
    COUNT(DISTINCT g.id) as exam_count,
    AVG(g.score) as avg_score,
    COUNT(DISTINCT n.id) as note_count,
    COUNT(DISTINCT wq.id) as wrong_question_count,
    COUNT(DISTINCT CASE WHEN wq.is_mastered = TRUE THEN wq.id END) as mastered_count
FROM users u
LEFT JOIN user_points up ON u.id = up.user_id AND up.status = 'COMPLETED'
LEFT JOIN grades g ON u.id = g.student_id AND g.status = 'PUBLISHED'
LEFT JOIN notes n ON u.id = n.student_id AND n.is_deleted = FALSE
LEFT JOIN wrong_questions wq ON u.id = wq.student_id AND wq.is_deleted = FALSE
WHERE u.is_deleted = FALSE
GROUP BY u.id, u.username, u.real_name, u.role, u.grade_level, u.class_name;

-- 创建学科统计视图
CREATE VIEW v_subject_statistics AS
SELECT
    s.id,
    s.name,
    s.subject_code,
    s.grade_level,
    COUNT(DISTINCT c.id) as chapter_count,
    COUNT(DISTINCT q.id) as question_count,
    COUNT(DISTINCT g.id) as exam_count,
    AVG(g.score) as avg_score,
    COUNT(DISTINCT n.id) as note_count
FROM subjects s
LEFT JOIN chapters c ON s.id = c.subject_id AND c.is_deleted = FALSE
LEFT JOIN questions q ON s.id = q.subject_id AND q.is_deleted = FALSE
LEFT JOIN grades g ON s.id = g.subject_id AND g.status = 'PUBLISHED'
LEFT JOIN notes n ON s.id = n.subject_id AND n.is_deleted = FALSE
WHERE s.is_deleted = FALSE
GROUP BY s.id, s.name, s.subject_code, s.grade_level;
