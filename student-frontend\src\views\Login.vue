<template>
  <div class="login-container">
    <div class="login-header">
      <van-image
        width="80"
        height="80"
        src="/logo.png"
        fit="contain"
        class="logo"
      />
      <h1>智能学习系统</h1>
      <p>让学习更智能，让成长更快乐</p>
    </div>

    <van-form @submit="handleLogin" class="login-form">
      <van-cell-group inset>
        <van-field
          v-model="loginForm.username"
          name="username"
          label="用户名"
          placeholder="请输入用户名"
          :rules="[{ required: true, message: '请输入用户名' }]"
          left-icon="contact"
        />
        <van-field
          v-model="loginForm.password"
          type="password"
          name="password"
          label="密码"
          placeholder="请输入密码"
          :rules="[{ required: true, message: '请输入密码' }]"
          left-icon="lock"
        />
      </van-cell-group>

      <div class="login-button">
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          :loading="loading"
          size="large"
        >
          登录
        </van-button>
      </div>
    </van-form>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { showToast } from 'vant'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const loading = ref(false)

const loginForm = reactive({
  username: '',
  password: ''
})

const handleLogin = async () => {
  loading.value = true
  try {
    const result = await authStore.login(loginForm)
    if (result.success) {
      showToast('登录成功')
      router.push('/')
    } else {
      showToast(result.message)
    }
  } catch (error) {
    showToast('登录失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60px 20px 20px;
}

.login-header {
  text-align: center;
  color: white;
  margin-bottom: 60px;
}

.logo {
  margin-bottom: 20px;
}

.login-header h1 {
  font-size: 28px;
  margin: 0 0 10px 0;
  font-weight: 300;
}

.login-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.8;
}

.login-form {
  margin-bottom: 40px;
}

.login-button {
  margin-top: 30px;
  padding: 0 16px;
}
</style>
