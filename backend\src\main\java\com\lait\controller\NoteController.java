package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.dto.NoteDTO;
import com.lait.service.NoteService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 笔记管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/notes")
@RequiredArgsConstructor
@Validated
public class NoteController {

    private final NoteService noteService;

    /**
     * 创建笔记
     */
    @PostMapping
    public ResponseEntity<ApiResponse<NoteDTO>> createNote(
            @Valid @RequestBody NoteDTO.CreateNoteRequest request) {
        log.info("创建笔记请求: {}", request.getTitle());
        NoteDTO noteDTO = noteService.createNote(request);
        return ResponseEntity.ok(ApiResponse.success(noteDTO));
    }

    /**
     * 根据ID获取笔记
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<NoteDTO>> getNoteById(@PathVariable Long id) {
        NoteDTO noteDTO = noteService.getNoteById(id);
        return ResponseEntity.ok(ApiResponse.success(noteDTO));
    }

    /**
     * 更新笔记信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<NoteDTO>> updateNote(
            @PathVariable Long id,
            @Valid @RequestBody NoteDTO.UpdateNoteRequest request) {
        log.info("更新笔记请求: {}", id);
        NoteDTO noteDTO = noteService.updateNote(id, request);
        return ResponseEntity.ok(ApiResponse.success(noteDTO));
    }

    /**
     * 删除笔记
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteNote(@PathVariable Long id) {
        log.info("删除笔记请求: {}", id);
        noteService.deleteNote(id);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 分页查询笔记
     */
    @GetMapping
    public ResponseEntity<ApiResponse<Page<NoteDTO>>> getNotes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<NoteDTO> notes = noteService.getNotes(pageable);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    /**
     * 根据学生ID分页查询笔记
     */
    @GetMapping("/student/{studentId}")
    public ResponseEntity<ApiResponse<Page<NoteDTO>>> getNotesByStudentId(
            @PathVariable Long studentId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<NoteDTO> notes = noteService.getNotesByStudentId(studentId, pageable);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    /**
     * 根据学科ID分页查询笔记
     */
    @GetMapping("/subject/{subjectId}")
    public ResponseEntity<ApiResponse<Page<NoteDTO>>> getNotesBySubjectId(
            @PathVariable Long subjectId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<NoteDTO> notes = noteService.getNotesBySubjectId(subjectId, pageable);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    /**
     * 搜索笔记
     */
    @PostMapping("/search")
    public ResponseEntity<ApiResponse<Page<NoteDTO>>> searchNotes(
            @RequestBody NoteDTO.NoteQueryRequest request,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<NoteDTO> notes = noteService.searchNotes(request, pageable);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    /**
     * 获取共享笔记
     */
    @GetMapping("/shared")
    public ResponseEntity<ApiResponse<Page<NoteDTO>>> getSharedNotes(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<NoteDTO> notes = noteService.getSharedNotes(pageable);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    /**
     * 根据标签查询笔记
     */
    @GetMapping("/tags/{tags}")
    public ResponseEntity<ApiResponse<Page<NoteDTO>>> getNotesByTags(
            @PathVariable String tags,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<NoteDTO> notes = noteService.getNotesByTags(tags, pageable);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    /**
     * 分享笔记到飞书
     */
    @PostMapping("/share-to-feishu")
    public ResponseEntity<ApiResponse<String>> shareNoteToFeishu(
            @Valid @RequestBody NoteDTO.ShareToFeishuRequest request) {
        log.info("分享笔记到飞书请求: {}", request.getNoteId());
        String feishuUrl = noteService.shareNoteToFeishu(request);
        return ResponseEntity.ok(ApiResponse.success(feishuUrl));
    }

    /**
     * 从飞书同步笔记
     */
    @PostMapping("/sync-from-feishu")
    public ResponseEntity<ApiResponse<NoteDTO>> syncNoteFromFeishu(
            @Valid @RequestBody NoteDTO.SyncFromFeishuRequest request) {
        log.info("从飞书同步笔记请求: {}", request.getFeishuDocId());
        NoteDTO noteDTO = noteService.syncNoteFromFeishu(request);
        return ResponseEntity.ok(ApiResponse.success(noteDTO));
    }

    /**
     * 获取学生笔记统计
     */
    @GetMapping("/statistics/student/{studentId}")
    public ResponseEntity<ApiResponse<NoteDTO.NoteStatistics>> getStudentNoteStatistics(
            @PathVariable Long studentId) {
        NoteDTO.NoteStatistics statistics = noteService.getStudentNoteStatistics(studentId);
        return ResponseEntity.ok(ApiResponse.success(statistics));
    }

    /**
     * 获取最近笔记
     */
    @GetMapping("/recent/student/{studentId}")
    public ResponseEntity<ApiResponse<List<NoteDTO>>> getRecentNotes(
            @PathVariable Long studentId,
            @RequestParam(defaultValue = "10") int limit) {
        
        List<NoteDTO> notes = noteService.getRecentNotes(studentId, limit);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    /**
     * 搜索笔记内容
     */
    @GetMapping("/search-content")
    public ResponseEntity<ApiResponse<Page<NoteDTO>>> searchNoteContent(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        Page<NoteDTO> notes = noteService.searchNoteContent(keyword, pageable);
        return ResponseEntity.ok(ApiResponse.success(notes));
    }

    /**
     * 批量删除笔记
     */
    @DeleteMapping("/batch")
    public ResponseEntity<ApiResponse<Void>> batchDeleteNotes(
            @RequestBody Long[] noteIds) {
        log.info("批量删除笔记请求，数量: {}", noteIds.length);
        noteService.batchDeleteNotes(noteIds);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 设置笔记分享状态
     */
    @PutMapping("/{id}/shared-status")
    public ResponseEntity<ApiResponse<Void>> setNoteSharedStatus(
            @PathVariable Long id,
            @RequestParam Boolean isShared) {
        log.info("设置笔记分享状态: {} -> {}", id, isShared);
        noteService.setNoteSharedStatus(id, isShared);
        return ResponseEntity.ok(ApiResponse.success(null));
    }

    /**
     * 获取热门标签
     */
    @GetMapping("/popular-tags")
    public ResponseEntity<ApiResponse<List<String>>> getPopularTags(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<String> tags = noteService.getPopularTags(limit);
        return ResponseEntity.ok(ApiResponse.success(tags));
    }

    /**
     * 复制笔记
     */
    @PostMapping("/{id}/duplicate")
    public ResponseEntity<ApiResponse<NoteDTO>> duplicateNote(
            @PathVariable Long id,
            @RequestParam Long targetStudentId) {
        log.info("复制笔记请求: {} -> {}", id, targetStudentId);
        NoteDTO noteDTO = noteService.duplicateNote(id, targetStudentId);
        return ResponseEntity.ok(ApiResponse.success(noteDTO));
    }
}
