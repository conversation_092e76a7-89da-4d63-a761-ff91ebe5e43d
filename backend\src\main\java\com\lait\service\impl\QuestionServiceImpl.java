package com.lait.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lait.dto.QuestionDTO;
import com.lait.entity.Question;
import com.lait.entity.Subject;
import com.lait.repository.QuestionRepository;
import com.lait.repository.SubjectRepository;
import com.lait.service.QuestionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 题目服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QuestionServiceImpl implements QuestionService {

    private final QuestionRepository questionRepository;
    private final SubjectRepository subjectRepository;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional
    public QuestionDTO createQuestion(QuestionDTO.CreateQuestionRequest request) {
        log.info("创建题目: {}", request.getContent());

        // 验证学科是否存在
        Subject subject = subjectRepository.findById(request.getSubjectId())
                .orElseThrow(() -> new RuntimeException("学科不存在"));

        Question question = new Question();
        BeanUtils.copyProperties(request, question);
        
        // 处理选项JSON
        if (request.getOptions() != null && !request.getOptions().isEmpty()) {
            try {
                question.setOptions(objectMapper.writeValueAsString(request.getOptions()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException("选项格式错误", e);
            }
        }

        Question savedQuestion = questionRepository.save(question);
        return convertToDTO(savedQuestion);
    }

    @Override
    public QuestionDTO getQuestionById(Long id) {
        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("题目不存在"));
        return convertToDTO(question);
    }

    @Override
    @Transactional
    public QuestionDTO updateQuestion(Long id, QuestionDTO.UpdateQuestionRequest request) {
        log.info("更新题目信息: {}", id);

        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        BeanUtils.copyProperties(request, question, "id", "subjectId", "usageCount", "correctCount");
        
        // 处理选项JSON
        if (request.getOptions() != null) {
            try {
                question.setOptions(objectMapper.writeValueAsString(request.getOptions()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException("选项格式错误", e);
            }
        }

        Question savedQuestion = questionRepository.save(question);
        return convertToDTO(savedQuestion);
    }

    @Override
    @Transactional
    public void deleteQuestion(Long id) {
        log.info("删除题目: {}", id);

        Question question = questionRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        question.setIsDeleted(true);
        questionRepository.save(question);
    }

    @Override
    public Page<QuestionDTO> getQuestions(Pageable pageable) {
        Page<Question> questions = questionRepository.findAllActive(pageable);
        return questions.map(this::convertToDTO);
    }

    @Override
    public Page<QuestionDTO> getQuestionsBySubjectId(Long subjectId, Pageable pageable) {
        Page<Question> questions = questionRepository.findBySubjectIdAndNotDeleted(subjectId, pageable);
        return questions.map(this::convertToDTO);
    }

    @Override
    public Page<QuestionDTO> searchQuestions(QuestionDTO.QuestionQueryRequest request, Pageable pageable) {
        // 这里可以根据request的条件构建复杂查询
        if (StringUtils.hasText(request.getKeyword())) {
            Page<Question> questions = questionRepository.searchQuestions(request.getKeyword(), pageable);
            return questions.map(this::convertToDTO);
        }
        return getQuestions(pageable);
    }

    @Override
    public Page<QuestionDTO> getQuestionsByDifficulty(Question.DifficultyLevel difficulty, Pageable pageable) {
        List<Question> questions = questionRepository.findByDifficulty(difficulty);
        // 这里简化处理，实际应该在Repository中实现分页查询
        return Page.empty(pageable);
    }

    @Override
    public Page<QuestionDTO> getQuestionsByType(Question.QuestionType type, Pageable pageable) {
        List<Question> questions = questionRepository.findByQuestionType(type);
        // 这里简化处理，实际应该在Repository中实现分页查询
        return Page.empty(pageable);
    }

    @Override
    public Page<QuestionDTO> getQuestionsByGradeLevel(Integer gradeLevel, Pageable pageable) {
        List<Question> questions = questionRepository.findByGradeLevel(gradeLevel);
        // 这里简化处理，实际应该在Repository中实现分页查询
        return Page.empty(pageable);
    }

    @Override
    public List<QuestionDTO> getRandomQuestions(Long subjectId, Question.DifficultyLevel difficulty, int count) {
        // 这里需要在Repository中添加相应的方法
        List<Question> questions = questionRepository.findBySubjectId(subjectId);
        return questions.stream()
                .filter(q -> q.getDifficulty() == difficulty)
                .limit(count)
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public List<QuestionDTO> batchImportQuestions(QuestionDTO.BatchImportRequest request) {
        log.info("批量导入题目，数量: {}", request.getQuestions().size());

        // 验证学科是否存在
        Subject subject = subjectRepository.findById(request.getSubjectId())
                .orElseThrow(() -> new RuntimeException("学科不存在"));

        return request.getQuestions().stream()
                .map(createRequest -> {
                    createRequest.setSubjectId(request.getSubjectId());
                    return createQuestion(createRequest);
                })
                .collect(Collectors.toList());
    }

    @Override
    public QuestionDTO.QuestionStatistics getQuestionStatistics(Long questionId) {
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        QuestionDTO.QuestionStatistics statistics = new QuestionDTO.QuestionStatistics();
        statistics.setQuestionId(questionId);
        statistics.setTitle(question.getContent());
        statistics.setUsageCount(question.getUsageCount());
        statistics.setCorrectCount(question.getCorrectCount());
        statistics.setDifficulty(question.getDifficulty());
        
        if (question.getUsageCount() > 0) {
            double correctRate = (double) question.getCorrectCount() / question.getUsageCount() * 100;
            statistics.setCorrectRate(correctRate);
        } else {
            statistics.setCorrectRate(0.0);
        }

        // 获取学科名称
        subjectRepository.findById(question.getSubjectId())
                .ifPresent(subject -> statistics.setSubjectName(subject.getName()));

        return statistics;
    }

    @Override
    @Transactional
    public void incrementUsageCount(Long questionId) {
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new RuntimeException("题目不存在"));
        
        question.setUsageCount(question.getUsageCount() + 1);
        questionRepository.save(question);
    }

    @Override
    @Transactional
    public void incrementCorrectCount(Long questionId) {
        Question question = questionRepository.findById(questionId)
                .orElseThrow(() -> new RuntimeException("题目不存在"));
        
        question.setCorrectCount(question.getCorrectCount() + 1);
        questionRepository.save(question);
    }

    @Override
    public List<QuestionDTO> getPopularQuestions(Long subjectId, int limit) {
        List<Question> questions = questionRepository.findBySubjectId(subjectId);
        return questions.stream()
                .sorted((q1, q2) -> Integer.compare(q2.getUsageCount(), q1.getUsageCount()))
                .limit(limit)
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<QuestionDTO> getMostWrongQuestions(Long subjectId, int limit) {
        List<Question> questions = questionRepository.findBySubjectId(subjectId);
        return questions.stream()
                .filter(q -> q.getUsageCount() > 0)
                .sorted((q1, q2) -> {
                    double rate1 = (double) q1.getCorrectCount() / q1.getUsageCount();
                    double rate2 = (double) q2.getCorrectCount() / q2.getUsageCount();
                    return Double.compare(rate1, rate2);
                })
                .limit(limit)
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public Page<QuestionDTO> getQuestionsByTags(String tags, Pageable pageable) {
        // 这里需要在Repository中添加相应的方法
        return Page.empty(pageable);
    }

    @Override
    @Transactional
    public QuestionDTO duplicateQuestion(Long questionId) {
        Question originalQuestion = questionRepository.findById(questionId)
                .orElseThrow(() -> new RuntimeException("题目不存在"));

        Question newQuestion = new Question();
        BeanUtils.copyProperties(originalQuestion, newQuestion, "id", "usageCount", "correctCount");
        newQuestion.setContent(originalQuestion.getContent() + " (副本)");
        
        Question savedQuestion = questionRepository.save(newQuestion);
        return convertToDTO(savedQuestion);
    }

    @Override
    @Transactional
    public void batchDeleteQuestions(Long[] questionIds) {
        for (Long questionId : questionIds) {
            deleteQuestion(questionId);
        }
    }

    /**
     * 转换为DTO
     */
    private QuestionDTO convertToDTO(Question question) {
        QuestionDTO dto = new QuestionDTO();
        BeanUtils.copyProperties(question, dto);
        
        // 设置学科名称
        subjectRepository.findById(question.getSubjectId())
                .ifPresent(subject -> dto.setSubjectName(subject.getName()));
        
        // 处理选项JSON
        if (StringUtils.hasText(question.getOptions())) {
            try {
                dto.setOptions(question.getOptions());
            } catch (Exception e) {
                log.warn("解析题目选项失败: {}", e.getMessage());
            }
        }
        
        return dto;
    }
}
