package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.security.JwtTokenProvider;
import com.lait.security.UserPrincipal;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

/**
 * 认证控制器
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

    private final AuthenticationManager authenticationManager;
    private final JwtTokenProvider tokenProvider;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public ApiResponse<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(
                            loginRequest.getUsername(),
                            loginRequest.getPassword()
                    )
            );

            SecurityContextHolder.getContext().setAuthentication(authentication);
            String jwt = tokenProvider.generateToken(authentication);

            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

            LoginResponse response = new LoginResponse();
            response.setAccessToken(jwt);
            response.setTokenType("Bearer");
            response.setUserId(userPrincipal.getId());
            response.setUsername(userPrincipal.getUsername());
            response.setRole(userPrincipal.getRole().name());

            return ApiResponse.success("登录成功", response);
        } catch (Exception e) {
            log.error("登录失败", e);
            return ApiResponse.unauthorized("用户名或密码错误");
        }
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    public ApiResponse<UserInfo> getCurrentUser(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

        UserInfo userInfo = new UserInfo();
        userInfo.setId(userPrincipal.getId());
        userInfo.setUsername(userPrincipal.getUsername());
        userInfo.setEmail(userPrincipal.getEmail());
        userInfo.setRole(userPrincipal.getRole().name());

        return ApiResponse.success(userInfo);
    }

    /**
     * 登录请求DTO
     */
    @Data
    public static class LoginRequest {
        @NotBlank(message = "用户名不能为空")
        private String username;

        @NotBlank(message = "密码不能为空")
        private String password;
    }

    /**
     * 登录响应DTO
     */
    @Data
    public static class LoginResponse {
        private String accessToken;
        private String tokenType;
        private Long userId;
        private String username;
        private String role;
    }

    /**
     * 用户信息DTO
     */
    @Data
    public static class UserInfo {
        private Long id;
        private String username;
        private String email;
        private String role;
    }
}
