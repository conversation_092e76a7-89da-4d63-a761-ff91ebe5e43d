<template>
  <div class="layout">
    <!-- 主内容区 -->
    <div class="main-content" :class="{ 'with-tabbar': $route.meta.showTabbar }">
      <router-view />
    </div>

    <!-- 底部导航栏 -->
    <van-tabbar
      v-if="$route.meta.showTabbar"
      v-model="activeTab"
      @change="onTabChange"
      fixed
      placeholder
    >
      <van-tabbar-item name="home" icon="home-o">
        首页
      </van-tabbar-item>
      <van-tabbar-item name="courses" icon="book-o">
        课程
      </van-tabbar-item>
      <van-tabbar-item name="practice" icon="edit">
        练习
      </van-tabbar-item>
      <van-tabbar-item name="profile" icon="user-o">
        我的
      </van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'

const router = useRouter()
const route = useRoute()

const activeTab = ref('home')

// 监听路由变化，更新活跃标签
watch(
  () => route.path,
  (newPath) => {
    const pathMap = {
      '/home': 'home',
      '/courses': 'courses',
      '/practice': 'practice',
      '/profile': 'profile'
    }
    activeTab.value = pathMap[newPath] || 'home'
  },
  { immediate: true }
)

// 标签切换处理
const onTabChange = (name) => {
  const routeMap = {
    home: '/home',
    courses: '/courses',
    practice: '/practice',
    profile: '/profile'
  }
  
  if (routeMap[name] && route.path !== routeMap[name]) {
    router.push(routeMap[name])
  }
}
</script>

<style scoped>
.layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  overflow-y: auto;
}

.main-content.with-tabbar {
  padding-bottom: 50px;
}
</style>
