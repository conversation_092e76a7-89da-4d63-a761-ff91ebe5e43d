package com.lait.controller;

import com.lait.dto.ApiResponse;
import com.lait.dto.QuestionDTO;
import com.lait.service.CozeApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * AI功能控制器
 */
@Slf4j
@RestController
@RequestMapping("/ai")
@RequiredArgsConstructor
@Validated
public class AiController {

    private final CozeApiService cozeApiService;

    /**
     * AI生成题目
     */
    @PostMapping("/generate-questions")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<List<QuestionDTO>>> generateQuestions(
            @Valid @RequestBody CozeApiService.GenerateQuestionRequest request) {
        log.info("AI生成题目请求: 学科={}, 类型={}, 数量={}", 
                request.getSubjectName(), request.getQuestionType(), request.getCount());
        
        List<QuestionDTO> questions = cozeApiService.generateQuestions(request);
        return ResponseEntity.ok(ApiResponse.success(questions));
    }

    /**
     * AI分析学生表现
     */
    @PostMapping("/analyze-performance")
    @PreAuthorize("hasRole('ADMIN') or hasRole('TEACHER')")
    public ResponseEntity<ApiResponse<CozeApiService.AnalysisResult>> analyzeStudentPerformance(
            @Valid @RequestBody CozeApiService.AnalysisRequest request) {
        log.info("AI分析学生表现请求: 学生ID={}, 学科ID={}", 
                request.getStudentId(), request.getSubjectId());
        
        CozeApiService.AnalysisResult result = cozeApiService.analyzeStudentPerformance(request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }

    /**
     * AI生成学习建议
     */
    @PostMapping("/study-advice")
    public ResponseEntity<ApiResponse<String>> generateStudyAdvice(
            @Valid @RequestBody CozeApiService.StudyAdviceRequest request) {
        log.info("AI生成学习建议请求: 学生ID={}, 学科ID={}", 
                request.getStudentId(), request.getSubjectId());
        
        String advice = cozeApiService.generateStudyAdvice(request);
        return ResponseEntity.ok(ApiResponse.success(advice));
    }

    /**
     * AI解释答案
     */
    @PostMapping("/explain-answer")
    public ResponseEntity<ApiResponse<String>> explainAnswer(
            @Valid @RequestBody CozeApiService.ExplainAnswerRequest request) {
        log.info("AI解释答案请求: 题目ID={}", request.getQuestionId());
        
        String explanation = cozeApiService.explainAnswer(request);
        return ResponseEntity.ok(ApiResponse.success(explanation));
    }

    /**
     * 获取AI功能状态
     */
    @GetMapping("/status")
    public ResponseEntity<ApiResponse<AiStatus>> getAiStatus() {
        AiStatus status = new AiStatus();
        status.setAvailable(true);
        status.setVersion("1.0.0");
        status.setProvider("Coze AI");
        status.setFeatures(List.of("题目生成", "表现分析", "学习建议", "答案解释"));
        
        return ResponseEntity.ok(ApiResponse.success(status));
    }

    /**
     * AI功能状态
     */
    public static class AiStatus {
        private boolean available;
        private String version;
        private String provider;
        private List<String> features;

        // Getters and Setters
        public boolean isAvailable() { return available; }
        public void setAvailable(boolean available) { this.available = available; }

        public String getVersion() { return version; }
        public void setVersion(String version) { this.version = version; }

        public String getProvider() { return provider; }
        public void setProvider(String provider) { this.provider = provider; }

        public List<String> getFeatures() { return features; }
        public void setFeatures(List<String> features) { this.features = features; }
    }
}
